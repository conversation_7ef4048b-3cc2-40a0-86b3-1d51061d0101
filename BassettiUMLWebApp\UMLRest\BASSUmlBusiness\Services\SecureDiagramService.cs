using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.AspNetCore.Http;

namespace BASSUmlBusiness.Services
{
    /// <summary>
    /// Secure wrapper for DiagramService that automatically handles permission validation
    /// Uses HttpContext to get pre-validated user information from ProjectPermissionAttribute
    /// </summary>
    public class SecureDiagramService : IDiagramService
    {
        private readonly DiagramService _diagramService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SecureDiagramService(DiagramService diagramService, IHttpContextAccessor httpContextAccessor)
        {
            _diagramService = diagramService;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets validated user email from HttpContext (set by ProjectPermissionAttribute)
        /// </summary>
        private string GetValidatedUserEmail()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items.ContainsKey("ValidatedUserEmail") == true)
            {
                return httpContext.Items["ValidatedUserEmail"]?.ToString() ?? string.Empty;
            }
            
            // Fallback to extracting from claims if not pre-validated
            var user = httpContext?.User;
            return user?.GetEmployeeEmail() ?? user?.GetEmail() ?? string.Empty;
        }

        /// <summary>
        /// Checks if the current request has been pre-validated by ProjectPermissionAttribute
        /// </summary>
        private bool IsRequestPreValidated(AccessType requiredAccess)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items.ContainsKey("ValidatedAccessType") == true)
            {
                var validatedAccessType = (AccessType)(httpContext.Items["ValidatedAccessType"] ?? AccessType.View);
                return HasSufficientAccess(validatedAccessType, requiredAccess);
            }
            
            return false;
        }

        /// <summary>
        /// Checks if the validated access type is sufficient for the required access
        /// </summary>
        private bool HasSufficientAccess(AccessType validatedAccess, AccessType requiredAccess)
        {
            // Admin has all permissions
            if (validatedAccess == AccessType.Admin) return true;
            
            // Edit access includes View permissions
            if (validatedAccess == AccessType.Edit && (requiredAccess == AccessType.Edit || requiredAccess == AccessType.View)) return true;
            
            // View access only for View requirements
            if (validatedAccess == AccessType.View && requiredAccess == AccessType.View) return true;
            
            return false;
        }

        // Implement IDiagramService interface with automatic permission handling
        public Diagram GetDiagramDetails(int idDiagram, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(AccessType.View))
            {
                email = GetValidatedUserEmail();
            }
            
            return _diagramService.GetDiagramDetails(idDiagram, email);
        }

        public Diagram GetDiagram(int idDiagram)
        {
            // Basic diagram retrieval - no permission check needed for internal use
            return _diagramService.GetDiagram(idDiagram);
        }

        public Diagram CreateDiagram(Diagram diagram, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(AccessType.Edit))
            {
                email = GetValidatedUserEmail();
            }
            
            return _diagramService.CreateDiagram(diagram, email);
        }

        public Diagram UpdateDiagram(Diagram diagram, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(AccessType.Edit))
            {
                email = GetValidatedUserEmail();
            }
            
            return _diagramService.UpdateDiagram(diagram, email);
        }

        public void DeleteDiagram(List<int> diagramIds, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(AccessType.Edit))
            {
                email = GetValidatedUserEmail();
            }
            
            _diagramService.DeleteDiagram(diagramIds, email);
        }

        public IList<Diagram> GetProjectDiagramsWithDetails(int idProject, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(AccessType.View))
            {
                email = GetValidatedUserEmail();
            }
            
            return _diagramService.GetProjectDiagramsWithDetails(idProject, email);
        }

        public Diagram MoveToFolder(Diagram diagram)
        {
            // Folder operations typically require edit access
            return _diagramService.MoveToFolder(diagram);
        }

        public Diagram RemoveFromFolder(int IdDiagram)
        {
            // Folder operations typically require edit access
            return _diagramService.RemoveFromFolder(IdDiagram);
        }
    }
}
