﻿using BASSUmlBusiness.Models;

namespace BASSUmlBusiness.Services.Abstractions
{
    public interface IDiagramService
    {
        /// <summary>
        /// Retrieves detailed information about a diagram including its associated classes and attributes.
        /// </summary>
        /// <param name="idDiagram">The ID of the diagram to retrieve.</param>
        /// <param name="email">User email for permission validation</param>
        /// <returns>The diagram with its associated classes and attributes.</returns>
        public Diagram GetDiagramDetails(int idDiagram, string email);
        /// <summary>
        /// Retrieves a diagram with the specified ID from the database.
        /// </summary>
        /// <param name="idDiagram">The ID of the diagram to retrieve.</param>
        /// <returns>The diagram object retrieved from the database.</returns>
        public Diagram GetDiagram(int idDiagram);
        /// <summary>
        /// Creates a new diagram.
        /// </summary>
        /// <param name="diagram">The diagram to be created.</param>
        /// <param name="email">User email for permission validation</param>
        /// <returns>The created diagram.</returns>
        /// <remarks>
        /// This method is responsible for creating a new diagram using the provided diagram details.
        /// </remarks>
        Diagram CreateDiagram(Diagram diagram, string email);
        /// <summary>
        /// Updates an existing diagram.
        /// </summary>
        /// <param name="diagram">The diagram with updated details.</param>
        /// <param name="email">User email for permission validation</param>
        /// <returns>The updated diagram.</returns>
        Diagram UpdateDiagram(Diagram diagram, string email);
        /// <summary>
        /// Deletes multiple diagrams based on its identifier.
        /// </summary>
        /// <param name="diagramIds">List of identifier of the diagrams to be deleted.</param>
        /// <param name="email">User email for permission validation</param>
        void DeleteDiagram(List<int> diagramIds, string email);

        /// <summary>
        /// Retrieves a list of diagrams associated with a specific project, including detailed information for each diagram.
        /// </summary>
        /// <param name="idProject">The unique identifier of the project for which to retrieve the diagrams.</param>
        /// <param name="email">User email for permission validation</param>
        /// <returns>A list of diagrams with detailed information for the specified project.</returns>
        public IList<Diagram> GetProjectDiagramsWithDetails(int idProject, string email);

        /// <summary>
        /// Move a diagram inside a folder
        /// </summary>
        /// <param name="diagram">Diagram object</param>
        /// <returns>Updated diagram</returns>
        Diagram MoveToFolder(Diagram diagram);

        /// <summary>
        /// Remove a diagram from a folder to it's project
        /// </summary>
        /// <param name="IdDiagram"></param>
        /// <returns>Updated diagram</returns>
        Diagram RemoveFromFolder(int IdDiagram);
    }
}
