using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Pagination;

namespace BASSUmlBusiness.Repositories
{
    /// <summary>
    /// Repository interface with built-in permission filtering
    /// All methods automatically apply user permission constraints
    /// </summary>
    public interface ISecureProjectRepository
    {
        /// <summary>
        /// Gets projects accessible to the current user with automatic permission filtering
        /// </summary>
        Task<IList<Project>> GetUserProjectsAsync(PaginationFilter filter, int currentPage = 1);

        /// <summary>
        /// Gets a project with permission validation
        /// </summary>
        /// <param name="projectId">Project ID</param>
        /// <param name="requiredAccess">Minimum required access level</param>
        Task<Project?> GetProjectAsync(int projectId, AccessType requiredAccess = AccessType.View);

        /// <summary>
        /// Gets project with diagrams and classes, with permission validation
        /// </summary>
        Task<Project?> GetProjectWithDetailsAsync(int projectId, AccessType requiredAccess = AccessType.View);

        /// <summary>
        /// Creates a new project (user automatically becomes admin)
        /// </summary>
        Task<Project> CreateProjectAsync(Project project);

        /// <summary>
        /// Updates a project with edit permission validation
        /// </summary>
        Task<Project> UpdateProjectAsync(Project project);

        /// <summary>
        /// Deletes a project with admin permission validation
        /// </summary>
        Task DeleteProjectAsync(int projectId);

        /// <summary>
        /// Checks if current user has specific access to a project
        /// </summary>
        Task<bool> HasAccessAsync(int projectId, AccessType requiredAccess);

        /// <summary>
        /// Gets user's access level for a project
        /// </summary>
        Task<AccessType?> GetUserAccessLevelAsync(int projectId);
    }

    /// <summary>
    /// Repository interface for diagrams with built-in permission filtering
    /// </summary>
    public interface ISecureDiagramRepository
    {
        /// <summary>
        /// Gets diagram details with permission validation
        /// </summary>
        Task<Diagram?> GetDiagramDetailsAsync(int diagramId, AccessType requiredAccess = AccessType.View);

        /// <summary>
        /// Gets all diagrams for a project with permission validation
        /// </summary>
        Task<IList<Diagram>> GetProjectDiagramsAsync(int projectId, AccessType requiredAccess = AccessType.View);

        /// <summary>
        /// Creates a new diagram with edit permission validation
        /// </summary>
        Task<Diagram> CreateDiagramAsync(Diagram diagram);

        /// <summary>
        /// Updates a diagram with edit permission validation
        /// </summary>
        Task<Diagram> UpdateDiagramAsync(Diagram diagram);

        /// <summary>
        /// Deletes diagrams with edit permission validation
        /// </summary>
        Task DeleteDiagramsAsync(List<int> diagramIds);

        /// <summary>
        /// Gets basic diagram info without permission check (for internal use)
        /// </summary>
        Task<Diagram?> GetDiagramAsync(int diagramId);
    }
}
