using AutoMapper;
using BASSUmlBusiness.BusinessModel;
using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Services.Abstractions;
using BASSUmlRest.DTOs.DiagramDTOs;
using BASSUmlRest.DTOs.ProjectDTOs;
using BASSUmlRest.DTOs.VersionHistoryDTOs;
using Microsoft.AspNetCore.Mvc;

namespace BASSUmlRest.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class VersionHistoryController : Controller
    {
        private readonly IMapper _mapper;
        private readonly IVersionHistoryService _versionHistoryService;
        private readonly IProjectService _projectService;
        private readonly IDiagramService _diagramService;

        public VersionHistoryController(IMapper mapper, IVersionHistoryService versionHistoryService, IProjectService projectService, IDiagramService diagramService)
        {
            _mapper = mapper;
            _versionHistoryService = versionHistoryService;
            _projectService = projectService;
            _diagramService = diagramService;
        }

        [HttpPost]
        public ActionResult<VersionHistoryDTO> CreateVersionHistory([FromBody] CreateVersionHistoryDTO versionHistory)
        {
            string? email = User.GetEmployeeEmail();
            if (email == null)
            {
                return BadRequest("Current User is not valid");
            }
            VersionHistoryDataDTO versionData = new VersionHistoryDataDTO()
            {
                ProjectDetails = _mapper.Map<Project, ProjectDetailsDTO>(_projectService.GetProjectWithDiagramsAndClasses(versionHistory.IdProject, email)),
                Diagrams = _mapper.Map<IList<Diagram>, IList<DiagramDetailsDTO>>(_diagramService.GetProjectDiagramsWithDetails(versionHistory.IdProject, email))
            };
            return Ok(_mapper.Map<VersionHistoryDetails, VersionHistoryDTO>(_versionHistoryService.CreateVersionHistory(_mapper.Map<CreateVersionHistoryDTO, VersionHistory>(versionHistory), _mapper.Map<VersionHistoryDataDTO, VersionData>(versionData), email)));
        }

        [HttpGet("project/{idProject}")]
        public ActionResult<IList<VersionHistoryDTO>> GetVersionHistories([FromRoute] int idProject)
        {
            return Ok(_mapper.Map<IList<VersionHistoryDetails>, IList<VersionHistoryDTO>>(_versionHistoryService.GetVersionHistories(idProject)));
        }
        [HttpGet("{idVersion}")]
        public ActionResult<string> GetVersion([FromRoute] int idVersion)
        {
            return Ok(_versionHistoryService.GetVersionHistory(idVersion));
        }

        [HttpPatch]
        public ActionResult<UpdateVersionHistoryDTO> UpdateVersionHistory([FromBody] UpdateVersionHistoryDTO versionHistory)
        {
            return Ok(_mapper.Map<UpdateVersionHistory, UpdateVersionHistoryDTO>(_versionHistoryService.UpdateVersionHistory(_mapper.Map<UpdateVersionHistoryDTO, UpdateVersionHistory>(versionHistory))));
        }

        [HttpDelete("{idVersionHistory}")]
        public ActionResult DeleteVersionHistory([FromRoute] int idVersionHistory)
        {
            _versionHistoryService.DeleteVersionHistory(idVersionHistory);
            return NoContent();
        }

    }
}


