import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { catchError, map, of } from 'rxjs';
import { PermissionApiService } from '../services/api/permission-api.service';
import { ErrorService } from '../services/errors/error.service';

export const accessGuard: CanActivateFn = (route, state) => {
  const permissionApiService = inject(PermissionApiService);
  const router = inject(Router);
  const errorService = inject(ErrorService);

  const navigation = history.state?.navigationId;
  if (!navigation) {
    localStorage.setItem('copyUrl', 'true');
  }

  // Extract project ID from route parameters
  const projectId = route.params['id'];

  if (!projectId) {
    // If no project ID, allow access (for dashboard, etc.)
    return true;
  }

  // Check user permission for the project
  return permissionApiService.checkPermission(Number(projectId)).pipe(
    map((permission) => {
      // If user has any permission (View, Edit, or Admin), allow access
      if (
        permission &&
        (permission.accessType === 0 ||
          permission.accessType === 1 ||
          permission.accessType === 2)
      ) {
        return true;
      } else {
        // User doesn't have permission, redirect to dashboard
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 403,
          type: 'error',
          header: 'Access Denied',
          content: 'You do not have permission to access this project.',
          isCustomError: true,
        });
        return false;
      }
    }),
    catchError((error) => {
      // Handle permission check error
      console.error('Error checking project permission:', error);

      if (error.status === 404) {
        // Project not found or user has no permission
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 404,
          type: 'error',
          header: 'Project Not Found',
          content:
            'The requested project was not found or you do not have access to it.',
          isCustomError: true,
        });
      } else {
        // Other errors
        router.navigate(['/dashboard']);
        errorService.addError({
          errorKey: 500,
          type: 'error',
          header: 'Access Check Failed',
          content: 'Unable to verify project access. Please try again.',
          isCustomError: true,
        });
      }

      return of(false);
    })
  );
};
