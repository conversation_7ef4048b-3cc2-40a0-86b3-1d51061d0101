{"ast": null, "code": "import * as go from 'gojs';\nimport { LinkLabelDraggingTool } from 'src/app/extensions/LinkLabelDraggingTool';\nimport { linkPortList } from 'src/app/shared/configs/linkPortConfig';\nimport { paletteConfigs } from 'src/app/shared/configs/palletteConfigs';\nimport { AttributeType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../access/access.service\";\nimport * as i2 from \"src/app/shared/utils/diagram-utils\";\nimport * as i3 from \"../property/property.service\";\nimport * as i4 from \"../diagram/event-listener.service\";\nimport * as i5 from \"./gojsClass/gojs-class.service\";\nimport * as i6 from \"./gojsEnumeration/gojs-enumeration.service\";\nimport * as i7 from \"./gojsLiteral/gojs-literal.service\";\nimport * as i8 from \"./gojsFolder/gojs-folder.service\";\nimport * as i9 from \"./gojsAttribute/gojs-attribute.service\";\nimport * as i10 from \"./gojsCardinality/gojs-cardinality.service\";\nimport * as i11 from \"./gojsCommon/gojs-common.service\";\nimport * as i12 from \"./gojs-comment/gojs-comment.service\";\nimport * as i13 from \"../treeNode/tree-node.service\";\nimport * as i14 from \"../snackbar/snack-bar.service\";\nimport * as i15 from \"../app.service\";\nexport let GojsService = /*#__PURE__*/(() => {\n  class GojsService {\n    constructor(accessService, diagramUtils, propertyService, eventListenerService, goJsClassService, goJsEnumService, goJsLiteralService, goJsFolderService, goJsAttributeService, goJsCardinalityService, goJsCommonService, gojsCommentService, treeNodeService, snackBarService, appService) {\n      this.accessService = accessService;\n      this.diagramUtils = diagramUtils;\n      this.propertyService = propertyService;\n      this.eventListenerService = eventListenerService;\n      this.goJsClassService = goJsClassService;\n      this.goJsEnumService = goJsEnumService;\n      this.goJsLiteralService = goJsLiteralService;\n      this.goJsFolderService = goJsFolderService;\n      this.goJsAttributeService = goJsAttributeService;\n      this.goJsCardinalityService = goJsCardinalityService;\n      this.goJsCommonService = goJsCommonService;\n      this.gojsCommentService = gojsCommentService;\n      this.treeNodeService = treeNodeService;\n      this.snackBarService = snackBarService;\n      this.appService = appService;\n      this.$ = go.GraphObject.make;\n      this._hasEditAccessOnly = false;\n      this._attributeTypes = [];\n      this._diagrams = [];\n      /**\n       * Handles the completion of a drop operation in the diagram editor\n       * @param {go.InputEvent} event is related to drop orientation\n       * @param {*} objectData is dragged object data\n       * @memberof DiagramEditorComponent\n       */\n      this.handleDropCompletion = (event, objectData) => {\n        if (!this._hasEditAccessOnly || this._diagrams.length == 0) {\n          event.diagram.currentTool.doCancel();\n          return;\n        }\n        const selectedObjects = event.diagram.selection;\n        selectedObjects.each(obj => {\n          if (obj.data && obj.data.category === GojsNodeCategory.Association) {\n            return;\n          }\n          if (objectData && objectData.data.supportingLevels.includes(obj.data.category)) {\n            this.handleMemberDrop(obj, objectData, event);\n          } else if (obj.data.isGroup) {\n            this.handleTopLevelDrop(obj, event);\n          } else {\n            event.diagram.remove(obj);\n            return;\n          }\n        });\n      };\n      this.accessService.accessTypeChanges().subscribe(access => {\n        if (access != AccessType.Viewer) {\n          this._hasEditAccessOnly = true;\n        } else {\n          this._hasEditAccessOnly = false;\n        }\n      });\n      this.goJsCommonService.gojsDiagramChanges().subscribe(diagram => {\n        if (diagram) this._gojsDiagram = diagram;\n      });\n      this.diagramUtils.getAttributeTypes().subscribe(options => {\n        this._attributeTypes = options.sort((a, b) => a.name.localeCompare(b.name, undefined, {\n          sensitivity: 'base'\n        }));\n        if (this._gojsDiagram && this._gojsDiagram.model) {\n          this._gojsDiagram.model.commit(model => {\n            model.set(model.modelData, 'attributeTypes', options);\n          }, null);\n        }\n      });\n      this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n        this._diagrams = diagrams;\n        if (this._gojsDiagram && this._diagrams.length == 0) {\n          this._gojsDiagram.clear();\n          this.snackBarService.warn('snackBar.diagramDeleteInfo');\n        }\n        if (this._gojsDiagram) {\n          this._gojsDiagram.allowDrop = this._hasEditAccessOnly && this._diagrams.length > 0;\n        }\n      });\n    }\n    /**\n     *Load the go js component  with the selected diagram details and set it to the layout container.\n     * @private\n     * @memberof DiagramEditorComponent\n     */\n    initDiagram(diagram) {\n      this.goJsCommonService.setGojsDiagram(diagram);\n      this._gojsDiagram.isReadOnly = !this._hasEditAccessOnly;\n      const toolManager = this._gojsDiagram.toolManager;\n      if (this._hasEditAccessOnly) {\n        toolManager.mouseMoveTools.insertAt(0, new LinkLabelDraggingTool());\n      } else {\n        const firstTool = toolManager.mouseMoveTools.elt(0);\n        if (firstTool instanceof LinkLabelDraggingTool) {\n          toolManager.mouseMoveTools.removeAt(0);\n        }\n      }\n      if (this.appService.isInitProject()) {\n        this.initializeMainDiagram();\n        this.eventListenerService.addDiagramEventListeners();\n        diagram.div?.addEventListener('dragover', event => {\n          event.preventDefault();\n        });\n        diagram.div?.addEventListener('drop', event => {\n          this.onDropNode(event);\n        });\n      }\n    }\n    onDropNode(event) {\n      event.preventDefault();\n      const data = event.dataTransfer?.getData('text/plain');\n      if (data) {\n        const nodeData = JSON.parse(data);\n        if (nodeData.category === GojsNodeCategory.Diagram) {\n          event.stopPropagation();\n          return;\n        }\n        // Get the drop position in screen coordinates (clientX, clientY)\n        const clientX = event.clientX - 300;\n        const clientY = event.clientY - 50;\n        // Convert the screen coordinates to diagram coordinates\n        const dropPoint = this._gojsDiagram.transformViewToDoc(new go.Point(clientX, clientY));\n        if (nodeData.category === GojsNodeCategory.Class || nodeData.category === GojsNodeCategory.AssociativeClass || nodeData.category === GojsNodeCategory.Enumeration) {\n          if (this.goJsCommonService.checkGroupNodeExist(this._gojsDiagram, nodeData)) return;\n          delete nodeData.data.key;\n          delete nodeData.data.id;\n          this.handleGroupNodeCreationOrUpdate({\n            ...nodeData.data,\n            position: go.Point.stringify(dropPoint)\n          }, true);\n        } else if (nodeData.category === GojsNodeCategory.Folder) {\n          const spacing = 170;\n          const children = this.treeNodeService.getClassesEnumsFromFolder(nodeData);\n          const nonExistNode = [];\n          children.forEach((child, index) => {\n            const newPoint = new go.Point(dropPoint.x + index * spacing, dropPoint.y);\n            if (!this.goJsCommonService.checkGroupNodeExist(this._gojsDiagram, child)) {\n              this.handleGroupNodeCreationOrUpdate({\n                ...child.data,\n                position: go.Point.stringify(newPoint)\n              }, true);\n            } else {\n              nonExistNode.push(child);\n            }\n          });\n          this.goJsCommonService.selectMultipleGroupNodeExist(this._gojsDiagram, nonExistNode);\n        }\n      }\n    }\n    initPaletteDiagram() {\n      const componentNodeTemplate = this.getComponentNodeTemplate();\n      const componentGroupTemplate = this.getComponentGroupTemplate();\n      this.configureComponentPalette(componentNodeTemplate, componentGroupTemplate);\n    }\n    /**\n     * Initializes the main diagram with configuration settings and tools.\n     * This method sets up the diagram's properties, tools, templates, and event listeners\n     * templates, and event listeners.\n     * @memberof DiagramEditorComponent\n     */\n    initializeMainDiagram() {\n      this.setupDiagramTools();\n      this.initializeItemTemplate();\n      this.configureGroupTemplate();\n      this.configureLinkTemplate();\n      this.configureGroupTemplateAdornment();\n      this.setupDiagramModel();\n    }\n    /**\n     * Sets up tools and context menus for the diagram.\n     * @private\n     * @memberof DiagramEditorComponent\n     */\n    setupDiagramTools() {\n      if (this._gojsDiagram && this._gojsDiagram.toolManager && this._gojsDiagram.toolManager.panningTool) {\n        this._gojsDiagram.toolManager.panningTool.isEnabled = this._hasEditAccessOnly;\n      }\n    }\n    /**\n     * Initializes and returns the item template for the diagram.\n     * This template includes text blocks for name, category, and type with appropriate bindings and styles.\n     *\n     * @private\n     * @returns {go.Panel} The configured item template panel.\n     * @memberof DiagramEditorComponent\n     */\n    initializeItemTemplate() {\n      return this.$(go.Panel, 'Horizontal', {\n        alignment: go.Spot.TopLeft,\n        stretch: go.Stretch.Fill\n      }, this.createNameTextBlock(), this.createCategoryTextBlock(), this.createTypeTextBlock());\n    }\n    /**\n     * Creates and returns a text block for the name with bindings and styles.\n     *\n     * @private\n     * @returns {go.TextBlock} The configured name text block.\n     * @memberof DiagramEditorComponent\n     */\n    createNameTextBlock() {\n      return this.$(go.TextBlock, {\n        isMultiline: false,\n        alignment: go.Spot.Center,\n        minSize: new go.Size(50, NaN),\n        margin: new go.Margin(3, 10, 0, 10),\n        overflow: go.TextOverflow.Ellipsis,\n        wrap: go.Wrap.Fit,\n        textEdited: (textBlock, oldText, newText) => {\n          if (!newText.trim()) {\n            // If the new text is empty or contains only spaces, restore the old value\n            textBlock.text = oldText;\n          }\n        }\n      }, new go.Binding('text', 'name').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n    }\n    /**\n     * Creates and returns a text block for the category with bindings and styles.\n     * @private\n     * @returns {go.TextBlock} The configured category text block.\n     * @memberof DiagramEditorComponent\n     */\n    createCategoryTextBlock() {\n      return this.$(go.TextBlock, ':', {\n        stroke: 'black'\n      }, new go.Binding('visible', 'category', category => {\n        return category === GojsNodeCategory.Attribute || category === GojsNodeCategory.Operation;\n      }));\n    }\n    /**\n     * Creates and returns a text block for the type with bindings, styles, and choices.\n     *\n     * @private\n     * @returns {go.TextBlock} The configured type text block.\n     * @memberof DiagramEditorComponent\n     */\n    createTypeTextBlock() {\n      return this.$(go.TextBlock, {\n        isMultiline: false,\n        alignment: go.Spot.Center,\n        margin: new go.Margin(3, 10, 0, 10),\n        textEditor: window.TextEditorSelectBox\n      }, new go.Binding('choices', '', () => {\n        return this._attributeTypes.sort();\n      }).makeTwoWay(), new go.Binding('text', 'dataType', dataType => {\n        const attributeOption = this._attributeTypes.find(option => option.id == dataType || option.name == dataType);\n        return attributeOption ? attributeOption.name : `${AttributeType[AttributeType.Undefined]}`;\n      }).makeTwoWay(), new go.Binding('name', 'dataType', dataType => {\n        const attributeOption = this._attributeTypes.find(option => option.id == dataType || option.name == dataType);\n        return attributeOption ? attributeOption.id : `0_${AttributeType[AttributeType.Undefined]}`;\n      }).makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay(), new go.Binding('visible', 'category', category => {\n        return category === GojsNodeCategory.Attribute || category === GojsNodeCategory.Operation;\n      }));\n    }\n    /**\n     * Configures the group template for the diagram with specific behaviors and styles.\n     * This method orchestrates the creation of group templates and applies them to the diagram.\n     *\n     * @private\n     * @memberof DiagramEditorComponent\n     */\n    configureGroupTemplate() {\n      this._gojsDiagram.groupTemplate = this.createGroupTemplate();\n      this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Package, this.createPackageGroupTemplate());\n      this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Enumeration, this.createEnumerationGroupTemplate());\n      this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.Comment, this.createCommentGroupTemplate());\n      this._gojsDiagram.groupTemplateMap.add(GojsNodeCategory.AssociativeClass, this.createAssociativeClassGroupTemplate());\n      this._gojsDiagram.nodeTemplateMap.add(GojsNodeCategory.LinkLabel, this.createLinkLabelTemplate());\n    }\n    /**\n     * Creates and returns the main group template for the diagram.\n     *\n     * @private\n     * @returns {go.Group} The configured group template.\n     * @memberof DiagramEditorComponent\n     */\n    createGroupTemplate() {\n      return this.$(go.Group, 'Auto', {\n        ...this.getClassOrAssociativeProperties()\n      },\n      //Binding the common property\n      ...this.getCommonBindings(), this.createGroupShape(), this.createGroupPanels(), ...linkPortList.map(linkPort => this.createPort(linkPort.portId, linkPort.alignment, linkPort.isFromLinkable, linkPort.isToLinkable)));\n    }\n    /**\n     * Creates or updates a class on the diagram.\n     *\n     * @param classData - The data of the class to be created or updated.\n     * @param event - The GoJS input event.\n     */\n    handleGroupNodeCreationOrUpdate(groupNodeData, isFromLibrary, event) {\n      if (groupNodeData['category'] === GojsNodeCategory.Class || groupNodeData['category'] === GojsNodeCategory.AssociativeClass) {\n        this.goJsClassService.handleClassCreationOrUpdate(groupNodeData, this._gojsDiagram, isFromLibrary, event);\n      } else if (groupNodeData['category'] === GojsNodeCategory.Enumeration) {\n        this.goJsEnumService.handleEnumCreationOrUpdate(groupNodeData, this._gojsDiagram, isFromLibrary, event);\n      }\n    }\n    /**\n     * Handles top-level drops in the diagram.\n     *\n     * @param obj - The dropped object.\n     * @param event - The GoJS input event.\n     */\n    handleTopLevelDrop(obj, event) {\n      if (obj.data.allowTopLevelDrops === true) {\n        event.diagram.commandHandler.addTopLevelParts(event.diagram.selection, true);\n        if (obj.data.category === GojsNodeCategory.Class || obj.data.category === GojsNodeCategory.Enumeration || obj.data.category === GojsNodeCategory.AssociativeClass) {\n          this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\n        } else if (obj.data.category === GojsNodeCategory.Comment) {\n          this.gojsCommentService.handleCommentDrop(obj.data, this._gojsDiagram);\n        }\n        event.diagram.clearSelection();\n      } else {\n        event.diagram.currentTool.doCancel();\n      }\n    }\n    /**\n     * Handles member element drops in the diagram.\n     * @param obj - The dropped object.\n     * @param objectData - The target object data.\n     * @param event - The GoJS input event.\n     */\n    handleMemberDrop(obj, objectData, event) {\n      if (objectData.data.supportingLevels.includes(obj.category) && !event.diagram.toolManager.textEditingTool.isActive) {\n        if (obj.data.category === GojsNodeCategory.Attribute || obj.data.category === GojsNodeCategory.Operation) {\n          this.goJsAttributeService.handleAttributeDrop(obj, objectData.data, event, this._gojsDiagram, this._hasEditAccessOnly);\n          this.handleSelectionDeleting(objectData);\n        } else if (obj.data.category === GojsNodeCategory.Class) {\n          this.handleGroupNodeCreationOrUpdate(obj.data, false, event);\n          objectData.addMembers(objectData.diagram.selection, true);\n        } else if (obj.data.category === GojsNodeCategory.EnumerationLiteral) {\n          this.goJsLiteralService.handleLiteralDrop(obj, objectData.data, event, this._gojsDiagram, this._hasEditAccessOnly);\n          this.handleSelectionDeleting(objectData);\n        }\n      } else {\n        const diagram = event.diagram;\n        // Remove the dropped node from the diagram\n        if (diagram && obj) {\n          diagram.remove(obj);\n        }\n        // Cancel the operation and clear selection\n        event.diagram.currentTool.doCancel();\n      }\n    }\n    /**\n     * Handles the selection deleting event for the diagram.\n     *\n     * @param objectData - The target object data.\n     */\n    handleSelectionDeleting(objectData) {\n      this._gojsDiagram.removeDiagramListener('SelectionDeleting', this.eventListenerService.addDeletingEventListener);\n      objectData.diagram.commandHandler.deleteSelection();\n      this._gojsDiagram.addDiagramListener('SelectionDeleting', this.eventListenerService.addDeletingEventListener);\n      this._gojsDiagram.model.updateTargetBindings(objectData);\n    }\n    /**\n     * Handles mouse drag enter events for groups.\n     *\n     * @private\n     * @param {go.DiagramEvent} _e - The diagram event.\n     * @param {go.GraphObject} grp - The group being entered.\n     * @param {go.GraphObject} _prev - The previous object.\n     */\n    handleMouseDragEnter(_e, grp, _prev) {\n      this.highlightGroup(grp, true);\n    }\n    createNewFolder(name, projectId) {\n      this.goJsFolderService.onCreateNewFolder(name, projectId, this._hasEditAccessOnly);\n    }\n    /**\n     * Handles mouse drag leave events for groups.\n     *\n     * @private\n     * @param {go.DiagramEvent} _e - The diagram event.\n     * @param {go.GraphObject} grp - The group being left.\n     * @param {go.GraphObject} _next - The next object.\n     */\n    handleMouseDragLeave(_e, grp, _next) {\n      this.highlightGroup(grp, false);\n    }\n    /**\n     * Handles selection changes for groups.\n     *\n     * @private\n     * @param {go.Part} node - The node whose selection changed.\n     */\n    handleSelectionChanged(node) {\n      this.propertyService.transferDataOnSelection(node);\n    }\n    /**\n     * Group shape color is highlighted or not\n     * @param {*} grp is used  to identify whether the element belongs to a group or\n     * @param {boolean} show is used for group visible or not\n     * @return {boolean}\n     * @memberof DiagramEditorComponent\n     */\n    highlightGroup(grp, show) {\n      if (!grp) return false;\n      // check that the drop may really happen into the Group\n      const tool = grp.diagram.toolManager.draggingTool;\n      grp.isHighlighted = show && grp.canAddMembers(tool.draggingParts);\n      return grp.isHighlighted;\n    }\n    /**\n     * Toggles the visibility of small ports on a node in the GoJS diagram.\n     *\n     * @param event - The input event that triggers the visibility change.\n     * @param node - The node whose ports will be shown or hidden.\n     * @param show - A boolean indicating whether to show or hide the ports.\n     */\n    toggleSmallPortsVisibility(node, show) {\n      node.ports.each(port => {\n        if (port.portId !== '') {\n          port.fill = show ? 'rgba(0, 0, 0, 0.3)' : null;\n        }\n      });\n      if (this._gojsDiagram && this._gojsDiagram.toolManager.linkingTool) {\n        this._gojsDiagram.toolManager.linkingTool.archetypeLinkData = {\n          category: node.data.category == GojsNodeCategory.AssociativeClass ? GojsNodeCategory.LinkToLink : GojsNodeCategory.Association\n        };\n      }\n    }\n    /**\n     * Creates the visual shape for groups.\n     * @private\n     * @returns {go.Shape} The shape configuration.\n     */\n    createGroupShape(additionalProperties = {}, isAssociative = false) {\n      const bindingProps = [new go.Binding('visible', 'showTablePanel').makeTwoWay(), new go.Binding('fromLinkable', 'editable').makeTwoWay(), new go.Binding('toLinkable', 'editable').makeTwoWay(), new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('fill', 'color', value => {\n        return this.$(go.Brush, 'Linear', {\n          0.0: this.goJsCommonService.updateRGBAColorWithOpacity(value, 0.4),\n          1.0: value\n        });\n      })];\n      if (!isAssociative) {\n        bindingProps.push(new go.Binding('stroke', 'color').makeTwoWay());\n      }\n      return this.$(go.Shape, 'RoundedRectangle', {\n        cursor: 'pointer',\n        ...additionalProperties\n      }, ...bindingProps);\n    }\n    /**\n     * Creates and returns the group panel with the specified properties and bindings.\n     * @returns {go.Panel} The group panel.\n     */\n    createGroupPanels() {\n      return this.$(go.Panel, 'Vertical', this.createTablePanel());\n    }\n    /**\n     * Creates and returns the table panel containing the components of the group panel.\n     * @returns {go.Panel} The table panel.\n     */\n    createTablePanel() {\n      return this.$(go.Panel, 'Table', {\n        name: 'Shape',\n        defaultRowSeparatorStroke: 'black',\n        defaultRowSeparatorStrokeWidth: 1,\n        portId: '',\n        cursor: 'pointer',\n        minSize: new go.Size(150, 100)\n      }, this.createRowColumnDefinitions(), this.createTitleTextBlock(), this.createPropertiesPanel(), this.createMethodsPanel(), new go.Binding('visible', 'showTablePanel').makeTwoWay(), new go.Binding('desiredSize', 'size').makeTwoWay());\n    }\n    /**\n     * Creates and returns the row and column definitions for the table panel.\n     * @returns {go.RowColumnDefinition[]} The row and column definitions.\n     */\n    createRowColumnDefinitions() {\n      return [this.$(go.RowColumnDefinition, {\n        row: 0,\n        minimum: 25,\n        maximum: 25,\n        stretch: go.Stretch.Fill,\n        separatorStrokeWidth: 1,\n        separatorStroke: 'black'\n      }), this.$(go.RowColumnDefinition, {\n        row: 1,\n        minimum: 60,\n        stretch: go.Stretch.Fill,\n        position: 20\n      })];\n    }\n    /**\n     * Creates and returns the title TextBlock for the table panel.\n     * @returns {go.TextBlock} The title TextBlock.\n     */\n    createTitleTextBlock() {\n      return this.$(go.TextBlock, {\n        row: 0,\n        columnSpan: 2,\n        font: 'bold 12pt sans-serif',\n        minSize: new go.Size(150, NaN),\n        margin: new go.Margin(0, 4, 0, 4),\n        isMultiline: false,\n        wrap: go.Wrap.Fit,\n        alignment: go.Spot.Center,\n        alignmentFocus: go.Spot.Center,\n        editable: true,\n        textAlign: 'center',\n        name: 'TEXTBLOCK',\n        textEdited: this.handleTableTextEdited.bind(this)\n      }, new go.Binding('text', 'name').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n    }\n    /**\n     * Handles the text edited event to update the property data.\n     * @param {go.TextBlock} textBlock - The TextBlock that was edited.\n     * @param {string} oldString - The old text string.\n     * @param {string} newString - The new text string.\n     */\n    handleTableTextEdited(textBlock, oldString, newString) {\n      if (!newString.trim()) {\n        // If the new text is empty or contains only spaces, restore the old value\n        textBlock.text = oldString;\n      } else {\n        const tableData = textBlock.part?.data;\n        if (tableData.category === GojsNodeCategory.Comment) {\n          this.gojsCommentService.updateComment(tableData.name, newString, tableData, this._gojsDiagram);\n        } else {\n          this.propertyService.transferDataOnSelection(textBlock.part);\n        }\n        if (tableData.category === GojsNodeCategory.Class || tableData.category === GojsNodeCategory.AssociativeClass) {\n          this.goJsClassService.updateTemplateClass({\n            ...tableData,\n            name: newString\n          }, this._gojsDiagram);\n        }\n        if (tableData.category === GojsNodeCategory.Enumeration) {\n          this.goJsEnumService.updateEnumerationFromDiagram({\n            ...tableData,\n            name: newString\n          }, this._gojsDiagram);\n        }\n      }\n    }\n    /**\n     * Creates and returns the properties panel for the table panel.\n     * @returns {go.Panel} The properties panel.\n     */\n    createPropertiesPanel() {\n      return this.$(go.Panel, 'Vertical', {\n        name: 'PROPERTIES',\n        row: 1,\n        stretch: go.Stretch.Fill,\n        alignment: go.Spot.Center,\n        itemTemplate: this.initializeItemTemplate()\n      }, new go.Binding('itemArray', 'items', items => items.filter(item => item.category == GojsNodeCategory.EnumerationLiteral || item.category == GojsNodeCategory.Attribute)), new go.Binding('visible', 'items', items => items.some(item => item.category === GojsNodeCategory.Attribute || item.category === GojsNodeCategory.EnumerationLiteral)));\n    }\n    /**\n     * Creates and returns the methods panel for the table panel.\n     * @returns {go.Panel} The methods panel.\n     */\n    createMethodsPanel() {\n      return this.$(go.Panel, 'Vertical', {\n        name: 'Methods',\n        row: 2,\n        stretch: go.Stretch.Fill,\n        alignment: go.Spot.Center,\n        itemTemplate: this.initializeItemTemplate()\n      }, new go.Binding('itemArray', 'items', items => items.filter(item => item.category == GojsNodeCategory.Operation)), new go.Binding('visible', 'items', items => items.some(item => item.category === GojsNodeCategory.Operation)));\n    }\n    /**\n     * Creates a port shape for a node in the GoJS diagram.\n     *\n     * @param portId - The unique identifier for the port.\n     * @param alignment - The alignment spot for the port on the node.\n     * @param isOutput - Specifies if the port is for outgoing links.\n     * @param isInput - Specifies if the port is for incoming links.\n     * @returns A GoJS Shape configured as a port.\n     */\n    createPort(portId, alignment, isOutput, isInput) {\n      return this.$(go.Shape, {\n        figure: 'Circle',\n        fill: 'transparent',\n        stroke: null,\n        desiredSize: new go.Size(9, 9),\n        alignment: alignment,\n        alignmentFocus: alignment,\n        portId: portId,\n        fromSpot: alignment,\n        toSpot: alignment,\n        fromLinkable: isOutput,\n        toLinkable: isInput,\n        fromLinkableSelfNode: isOutput,\n        toLinkableSelfNode: isInput,\n        cursor: 'pointer'\n      });\n    }\n    /**\n     * Creates a specialized group template for packages.\n     *\n     * @private\n     * @returns {go.Group} The package group template.\n     */\n    createPackageGroupTemplate() {\n      return this.$(go.Group, 'Auto', {\n        background: 'blue',\n        ungroupable: true,\n        mouseDragEnter: (_e, grp) => this.highlightGroup(grp, true),\n        mouseDragLeave: (_e, grp) => this.highlightGroup(grp, false),\n        computesBoundsAfterDrag: true,\n        computesBoundsIncludingLocation: true,\n        mouseDrop: this.handleDropCompletion,\n        handlesDragDropForMembers: true,\n        resizable: true,\n        resizeObjectName: 'Placeholder'\n      }, this.createPackageGroupShape(), this.createPackageGroupPanels());\n    }\n    /**\n     * Creates the visual shape for package groups.\n     *\n     * @private\n     * @returns {go.Shape} The shape configuration.\n     */\n    createPackageGroupShape() {\n      return this.$(go.Shape, 'RoundedRectangle', {\n        stroke: this.defaultColor(true),\n        fill: this.defaultColor(true),\n        strokeWidth: 2\n      }, new go.Binding('stroke', 'horiz', this.defaultColor), new go.Binding('fill', 'horiz', this.defaultColor));\n    }\n    /**\n     * Creates panels for package groups.\n     *\n     * @private\n     * @returns {go.Panel} The panel configuration.\n     */\n    createPackageGroupPanels() {\n      return this.$(go.Panel, 'Vertical', {\n        name: 'Placeholder'\n      }, this.createPackageGroupHeader(), this.$(go.Placeholder, {\n        padding: 5,\n        alignment: go.Spot.LeftCenter,\n        minSize: new go.Size(200, 150)\n      }));\n    }\n    createCommentGroupPanels() {\n      return this.$(go.Panel, 'Vertical', {\n        name: 'Placeholder'\n      }, this.createPackageGroupHeader(), this.$(go.TextBlock, {\n        font: 'bold 10pt Helvetica, Arial, sans-serif',\n        margin: new go.Margin(10, 8, 4, 8),\n        textAlign: 'left',\n        maxLines: Infinity,\n        minSize: new go.Size(NaN, 200),\n        wrap: go.Wrap.Fit,\n        // isMultiline: false,\n        alignment: go.Spot.TopLeft,\n        overflow: go.TextOverflow.Clip,\n        stretch: go.Stretch.Fill,\n        textEdited: this.handleTableTextEdited.bind(this),\n        mouseDrop: (e, _obj) => e.diagram.currentTool.doCancel()\n      }, new go.Binding('text', 'description').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay(), new go.Binding('desiredSize', 'size').makeTwoWay()));\n    }\n    /**\n     * Creates the header panel for package groups.\n     *\n     * @private\n     * @returns {go.Panel} The header panel configuration.\n     */\n    createPackageGroupHeader() {\n      return this.$(go.Panel, 'Table', {\n        stretch: go.Stretch.Horizontal,\n        background: this.defaultColor(true)\n      }, new go.Binding('background', 'horiz', this.defaultColor), this.$(go.TextBlock, {\n        alignment: go.Spot.Left,\n        stretch: go.Stretch.Horizontal,\n        editable: true,\n        isMultiline: false,\n        wrap: go.Wrap.Fit,\n        margin: 5,\n        font: this.defaultFont(false),\n        opacity: 0.95,\n        stroke: '#404040',\n        textEdited: this.handleTextEdited.bind(this)\n      }, new go.Binding('font', 'horiz', this.defaultFont), new go.Binding('text', 'name').makeTwoWay()));\n    }\n    handleTextEdited(textBlock, _oldString, newString) {\n      const tableData = textBlock.diagram?.selection.first()?.data;\n      if (tableData.category === GojsNodeCategory.Comment) {\n        this.gojsCommentService.updateComment(newString, tableData.description, tableData, this._gojsDiagram);\n      }\n    }\n    /**\n     *For getting the default color\n     * @private\n     * @param {boolean} horiz is boolean  value for horizontal or vertical layout\n     * @return {*}  {string}\n     * @memberof DiagramEditorComponent\n     */\n    defaultColor(horiz) {\n      // a Binding conversion function\n      return horiz ? 'rgba(255, 221, 51, 0.55)' : 'rgba(51,211,229, 0.5)';\n    }\n    /**\n     *For getting the default font\n     * @param {boolean} horiz is boolean  value for horizontal or vertical layout\n     * @return {*}  {string}\n     * @memberof DiagramEditorComponent\n     */\n    defaultFont(horiz) {\n      // a Binding conversion function\n      return horiz ? 'bold 20px sans-serif' : 'bold 16px sans-serif';\n    }\n    createEnumerationGroupTemplate() {\n      return this.$(go.Group, 'Auto', {\n        selectionAdornmentTemplate: this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(false)),\n        ...this.getCommonGroupProperties()\n      },\n      //Binding the common property\n      ...this.getCommonBindings(), this.createGroupShape(), this.createGroupPanels());\n    }\n    /**\n     * Creates a button for adding attributes to the group node.\n     *\n     * @returns {go.Panel} The configured attribute button panel.\n     */\n    createAttributeButton(isForLiteral) {\n      return this.createActionButton(!isForLiteral ? GoJsNodeIcon.Attribute : GoJsNodeIcon.EnumerationLiteral, 'Attribute', (_e, obj) => isForLiteral ? this.addNodeLiteral(obj) : this.goJsAttributeService.addNodeAttribute(obj, 'Attribute', GojsNodeCategory.Attribute, this._gojsDiagram));\n    }\n    /**\n     * Creates a button with an image and a specified click handler.\n     * This method helps in reducing redundancy by allowing the creation of multiple buttons with similar structure.\n     *\n     * @param {string} imagePath - The path to the button image.\n     * @param {Function} clickHandler - The function to be executed on button click.\n     * @returns {go.Panel} The configured button panel.\n     */\n    createActionButton(iconUnicode,\n    // Accepts Font Awesome Unicode (e.g., '\\uf1fe')\n    tooltipText,\n    // Tooltip text to display on hover\n    clickHandler) {\n      return this.$('Button', {\n        click: clickHandler,\n        margin: new go.Margin(0, 10, 0, 0),\n        toolTip: this.$(go.Adornment,\n        // Tooltip container\n        'Auto',\n        // Auto layout for proper sizing\n        {\n          alignment: go.Spot.Top,\n          alignmentFocus: go.Spot.Bottom // Tooltip bottom edge aligns with button's top edge\n        }, this.$(go.Shape, {\n          fill: 'white',\n          stroke: null\n        } // Tooltip background\n        ), this.$(go.TextBlock, {\n          font: '10pt sans-serif',\n          margin: 5,\n          textAlign: 'center',\n          wrap: go.Wrap.Fit,\n          alignment: go.Spot.Top,\n          alignmentFocus: go.Spot.Top // Tooltip bottom edge aligns with button's top edge\n        }, tooltipText // Tooltip text content\n        ))\n      }, this.$(go.TextBlock,\n      // Use a TextBlock for Font Awesome icons\n      {\n        font: '14px FontAwesome',\n        text: iconUnicode,\n        textAlign: 'center',\n        verticalAlignment: go.Spot.Center,\n        desiredSize: new go.Size(15, 15) // Adjust size as needed\n      }));\n    }\n    /**\n     * Adds a literal node to the currently selected node in the GoJS diagram.\n     *\n     * This method retrieves the currently selected node in the diagram and uses\n     * the `GoJsLiteralService` to create a new literal node associated with the selected node.\n     * The `idTemplateEnumeration` is passed to help identify the template enumeration.\n     *\n     * @param obj - The GoJS `GraphObject` from which the selected node is retrieved.\n     */\n    addNodeLiteral(obj) {\n      // Get the currently selected node in the diagram\n      const selectedNode = obj.diagram.selection.first();\n      // Extract the `idTemplateEnumeration` from the selected node's data\n      const idTemplateEnumeration = selectedNode.data.idTemplateEnumeration;\n      // Use the GoJsLiteralService to create a literal node\n      this.goJsLiteralService.onCreateLiteral(selectedNode.data, 'Literal', idTemplateEnumeration, this._gojsDiagram);\n    }\n    /**\n     * Configures the link template for the diagram with specific behaviors and styles.\n     * This method orchestrates the creation of link templates and applies them to the diagram.\n     *\n     * @private\n     * @memberof DiagramEditorComponent\n     */\n    configureLinkTemplate() {\n      this._gojsDiagram.linkTemplate = this.createLinkTemplate();\n      this._gojsDiagram.linkTemplateMap.add(GojsNodeCategory.LinkToLink, this.createLinkToLinkTemplate());\n    }\n    /**\n     * Creates and returns the link template for the diagram.\n     *\n     * @private\n     * @returns {go.Link} The configured link template.\n     * @memberof DiagramEditorComponent\n     */\n    createLinkTemplate() {\n      return this.$(go.Link, {\n        selectionChanged: this.handleSelectionChanged.bind(this)\n      }, this.customizeLinkTemplate(), new go.Binding('selectable', 'editable').makeTwoWay(), new go.Binding('reshapable', 'editable').makeTwoWay(), new go.Binding('resegmentable', 'editable').makeTwoWay(), new go.Binding('relinkableFrom', 'editable').makeTwoWay(), new go.Binding('relinkableTo', 'editable').makeTwoWay(), new go.Binding('deletable', 'editable').makeTwoWay(), new go.Binding('toLinkableDuplicates', 'editable').makeTwoWay(),\n      // Instead of 'editable', you might use the following bindings:\n      new go.Binding('fromLinkable', 'editable').makeTwoWay(), new go.Binding('toLinkable', 'editable').makeTwoWay(), new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(), new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(), this.createLinkShape(), this.createLinkNameShape('name'), this.createCardinalityTextBlock('Cardinality1', 0, 0.5, new go.Point(15, -15), 'cardinalityTo'), this.createCardinalityTextBlock('Cardinality2', -1, 0.5, new go.Point(-15, -15), 'cardinalityFrom'), this.$(go.TextBlock, {\n        // text: 'fromComment',\n        segmentIndex: 0,\n        segmentOffset: new go.Point(NaN, 15),\n        editable: true,\n        maxSize: new go.Size(200, NaN),\n        overflow: go.TextOverflow.Ellipsis,\n        wrap: go.Wrap.Fit,\n        isMultiline: false\n      }, new go.Binding('text', 'fromComment').makeTwoWay()), this.$(go.TextBlock, {\n        // text: 'toComment',\n        segmentIndex: -1,\n        segmentOffset: new go.Point(NaN, 15),\n        editable: true,\n        maxSize: new go.Size(200, NaN),\n        overflow: go.TextOverflow.Ellipsis,\n        wrap: go.Wrap.Fit,\n        isMultiline: false\n      }, new go.Binding('text', 'toComment').makeTwoWay()));\n    }\n    createLinkToLinkTemplate() {\n      return this.$(go.Link, {\n        deletable: false,\n        movable: false\n      }, new go.Binding('relinkableFrom', 'editable'), new go.Binding('relinkableTo', 'editable'), new go.Binding('selectable', 'editable'), new go.Binding('reshapable', 'editable'), this.$(go.Shape, {\n        fill: 'black',\n        strokeWidth: 2,\n        strokeDashArray: [4, 8]\n      }));\n    }\n    createLinkNameShape(bindingProp) {\n      return this.$(go.Panel, 'Auto', this.$(go.Shape, {\n        fill: this.$(go.Brush, 'Radial'),\n        stroke: 'transparent',\n        background: 'white'\n      }), this.$(go.TextBlock, {\n        textAlign: 'center',\n        font: '10pt helvetica, arial, sans-serif',\n        stroke: 'black',\n        margin: 4,\n        isMultiline: false,\n        editable: true\n      }, new go.Binding('text', bindingProp).makeTwoWay(), new go.Binding('segmentFraction', 'labelPosition.segmentFraction').makeTwoWay()), new go.Binding('segmentOffset', 'segmentOffset', go.Point.parse).makeTwoWay(go.Point.stringify));\n    }\n    /**\n     * For customize the link shape and style in pallette\n     * @private\n     * @returns {go.Link}\n     * @memberOf DiagramEditorComponent\n     */\n    customizeLinkTemplate() {\n      return {\n        routing: go.Routing.AvoidsNodes,\n        corner: 5,\n        fromEndSegmentLength: 30,\n        toEndSegmentLength: 30,\n        curve: go.Curve.JumpOver,\n        toShortLength: 4\n      };\n    }\n    /**\n     * Creates a shape for the link.\n     * @private\n     * @returns {go.Shape} The shape configuration.\n     */\n    createLinkShape() {\n      return this.$(go.Shape, {\n        segmentFraction: 10,\n        width: 100,\n        strokeWidth: 1.2 // Add a slightly thicker stroke for better visibility\n      },\n      // Add color bindings\n      new go.Binding('stroke', 'color').makeTwoWay(), new go.Binding('strokeWidth', 'isHighlighted', h => h ? 2.5 : 1.5).ofObject());\n    }\n    /**\n     * Creates a text block for cardinality with specified bindings, choices, and styles.\n     * @private\n     * @param {string} name - The name of the text block.\n     * @param {number} segmentIndex - The segment index.\n     * @param {number} segmentFraction - The segment fraction.\n     * @param {go.Point} segmentOffset - The segment offset.\n     * @param {string} cardinalityText - The cardinality name\n     * @returns {go.TextBlock} The configured cardinality text block.\n     */\n    createCardinalityTextBlock(name, segmentIndex, segmentFraction, segmentOffset, cardinalityText) {\n      return this.$(go.TextBlock, {\n        name: name,\n        segmentIndex: segmentIndex,\n        segmentFraction: segmentFraction,\n        segmentOffset: segmentOffset,\n        textEditor: window.TextEditorSelectBox,\n        choices: ['0..1', '1', '*', '1..*']\n      }, new go.Binding('text', cardinalityText).makeTwoWay(), new go.Binding('choices', 'choices').makeTwoWay(), new go.Binding('editable', 'editable').makeTwoWay());\n    }\n    /**\n     * Configures the selection adornment template for group nodes in the diagram.\n     * The adornment includes a border and buttons for adding attributes, methods, and drawing links.\n     *\n     * @private\n     *\n     * @memberOf DiagramEditorComponent\n     */\n    configureGroupTemplateAdornment() {\n      this._gojsDiagram.groupTemplate.selectionAdornmentTemplate = this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(true, false));\n    }\n    /**\n     * Creates an Auto Panel that serves as the background for the adornment,\n     * with a border to highlight the selected group node.\n     * @private\n     *\n     * @returns {go.Panel} The configured Auto Panel.\n     * @memberOf DiagramEditorComponent\n     */\n    createSelectionBorderPanel() {\n      return this.$(go.Panel, 'Auto', this.$(go.Shape, {\n        stroke: 'dodgerblue',\n        strokeWidth: 2,\n        fill: null\n      }), this.$(go.Placeholder));\n    }\n    /**\n     * Creates a Horizontal Panel that contains buttons for adding attributes, methods, and drawing links.\n     * The panel is aligned at the top of the group node and positioned just below it.\n     *\n     * @returns {go.Panel} The configured Horizontal Panel.\n     */\n    createActionButtonPanel(isClass, isAssociative = false) {\n      const actionPanels = [];\n      if (isClass) {\n        actionPanels.push(this.createAttributeButton(false));\n        actionPanels.push(this.createMethodButton());\n        if (!isAssociative) actionPanels.push(this.createLinkButton());\n      } else {\n        actionPanels.push(this.createAttributeButton(true));\n      }\n      return this.$(go.Panel, 'Horizontal', {\n        alignment: go.Spot.Top,\n        alignmentFocus: go.Spot.Bottom\n      }, ...actionPanels);\n    }\n    /**\n     * Creates a button for adding methods to the group node.\n     * @returns {go.Panel} The configured method button panel.\n     */\n    createMethodButton() {\n      return this.createActionButton(GoJsNodeIcon.Operation, 'Method', (_e, obj) => this.goJsAttributeService.addNodeAttribute(obj, 'Method', GojsNodeCategory.Operation, this._gojsDiagram));\n    }\n    /**\n     * Creates a button for drawing links between nodes.\n     * The button supports both click and drag actions to initiate a link drawing operation.\n     * @returns {go.Panel} The configured link button panel.\n     */\n    createLinkButton() {\n      return this.$('Button', {\n        click: (e, obj) => this.initiateLinkDrawing(e, obj),\n        actionMove: (e, obj) => this.initiateLinkDrawing(e, obj),\n        toolTip: this.$(go.Adornment,\n        // Tooltip container\n        'Auto',\n        // Auto layout for proper sizing\n        {\n          alignment: go.Spot.Top,\n          alignmentFocus: go.Spot.Bottom // Tooltip bottom edge aligns with button's top edge\n        }, this.$(go.Shape, {\n          fill: 'white',\n          stroke: null\n        } // Tooltip background\n        ), this.$(go.TextBlock, {\n          font: '10pt sans-serif',\n          margin: 5,\n          textAlign: 'center',\n          wrap: go.Wrap.Fit,\n          alignment: go.Spot.Top,\n          alignmentFocus: go.Spot.Top // Tooltip bottom edge aligns with button's top edge\n        }, 'Draw Link' // Tooltip text content\n        ))\n      }, this.$(go.Shape, {\n        geometryString: 'M0 0 L8 0 8 12 14 12 M12 10 L14 12 12 14',\n        desiredSize: new go.Size(15, 15),\n        fill: 'lightyellow'\n      }));\n    }\n    /**\n     * Initiates the linking tool to draw a link from the selected node.\n     * @param {go.InputEvent} event - The input event triggering the link creation.\n     * @param {go.GraphObject} button - The button that was clicked to start drawing the link.\n     */\n    initiateLinkDrawing(event, button) {\n      const selectedNode = button.part.adornedPart;\n      const linkingTool = event.diagram.toolManager.linkingTool;\n      const specificPort = selectedNode.findPort('R2');\n      if (specificPort) {\n        linkingTool.startObject = specificPort;\n        event.diagram.currentTool = linkingTool;\n        linkingTool.doActivate();\n      } else {\n        console.error('Port not found on the selected node');\n      }\n    }\n    /**\n     * Sets up the model for the diagram\n     *\n     * @private\n     * @memberof DiagramEditorComponent\n     */\n    setupDiagramModel() {\n      this.diagramUtils.initializeDiagramModelData(this._gojsDiagram);\n    }\n    createCommentGroupTemplate() {\n      return this.$(go.Group, 'Auto', {\n        background: 'blue',\n        ungroupable: true,\n        computesBoundsAfterDrag: true,\n        computesBoundsIncludingLocation: true,\n        isSubGraphExpanded: false,\n        handlesDragDropForMembers: true,\n        resizable: true,\n        resizeObjectName: 'Placeholder'\n      },\n      //Binding the common property\n      ...this.getCommonBindings(), new go.Binding('background', 'isHighlighted', h => h ? 'rgba(255,0,0,0.2)' : 'transparent').ofObject(), this.createPackageGroupShape(), this.createCommentGroupPanels());\n    }\n    /**\n     * Creates and returns a node template for the palette.\n     *\n     * @private\n     * @returns {go.Node} The configured node template.\n     * @memberof DiagramEditorComponent\n     */\n    getComponentNodeTemplate() {\n      return this.$(go.Node, 'Horizontal', this.$(go.TextBlock,\n      // Replace Picture with TextBlock for Font Awesome icons\n      {\n        width: 14,\n        height: 14,\n        font: '13px FontAwesome',\n        textAlign: 'center',\n        verticalAlignment: go.Spot.Center,\n        margin: new go.Margin(0, 5, 0, 0) // Adjust margin as needed\n      }, new go.Binding('text', 'icon') // Bind to the icon property\n      ), this.$(go.TextBlock, {\n        stroke: 'black',\n        font: '10pt sans-serif',\n        editable: true,\n        isMultiline: false,\n        cursor: 'pointer',\n        portId: ''\n      }, new go.Binding('text', 'name')));\n    }\n    /**\n     * Creates and returns a group template for the palette.\n     *\n     * @private\n     * @returns {go.Group} The configured group template.\n     * @memberof DiagramEditorComponent\n     */\n    getComponentGroupTemplate() {\n      return this.$(go.Group, 'Horizontal', {\n        cursor: 'pointer'\n      }, new go.Binding('selectable', 'editable').makeTwoWay(), this.$(go.TextBlock,\n      // Replace Picture with TextBlock for Font Awesome icons\n      {\n        height: 14,\n        width: 14,\n        font: '13px FontAwesome',\n        textAlign: 'center',\n        verticalAlignment: go.Spot.Center,\n        margin: new go.Margin(0, 5, 0, 0) // Adjust margin as needed\n      }, new go.Binding('text', 'icon') // Bind to the icon property\n      ), this.$(go.TextBlock, {\n        name: 'Label',\n        stroke: 'black',\n        font: '10pt sans-serif',\n        alignment: go.Spot.Right,\n        alignmentFocus: go.Spot.Left\n      }, new go.Binding('text', 'name')));\n    }\n    createAssociativeClassGroupTemplate() {\n      return this.$(go.Group, 'Auto', {\n        selectionAdornmentTemplate: this.$(go.Adornment, 'Spot', this.createSelectionBorderPanel(), this.createActionButtonPanel(true, true)),\n        ...this.getClassOrAssociativeProperties()\n      },\n      //Binding the common property\n      ...this.getCommonBindings(), this.createGroupShape({\n        stroke: 'black',\n        strokeWidth: 1.5,\n        strokeDashArray: [4, 8]\n      }, true), this.createGroupPanels(), ...linkPortList.map(linkPort => this.createPort(linkPort.portId, linkPort.alignment, linkPort.isFromLinkable, linkPort.isToLinkable)));\n    }\n    createLinkLabelTemplate() {\n      return this.$(go.Node, {\n        avoidable: true,\n        layerName: 'Foreground',\n        movable: false,\n        deletable: false,\n        fromLinkableSelfNode: false\n      }, new go.Binding('selectable', 'editable').makeTwoWay(), this.$(go.Shape, 'Ellipse', {\n        width: 0.5,\n        height: 0.5,\n        fill: 'rgba(0,0,0,0.01)',\n        stroke: 'rgba(0,0,0,0.05)',\n        portId: '',\n        cursor: 'pointer',\n        fromLinkable: false\n      }, new go.Binding('toLinkable', 'editable').makeTwoWay()));\n    }\n    /**\n     * Retrieves common properties and event handlers shared across all group elements in the diagram.\n     * @private\n     * @return {*}  {go.ObjectData} An object containing properties and event handlers for group elements.\n     * @memberof GojsService\n     */\n    getCommonGroupProperties() {\n      return {\n        mouseDragEnter: () => this.handleMouseDragEnter.bind(this),\n        mouseDragLeave: () => this.handleMouseDragLeave.bind(this),\n        computesBoundsAfterDrag: true,\n        computesBoundsIncludingLocation: true,\n        mouseDrop: this.handleDropCompletion,\n        resizeObjectName: 'Shape',\n        selectionChanged: this.handleSelectionChanged.bind(this)\n      };\n    }\n    /**\n     * Retrieves properties configuration for Class and Associative Class elements in the diagram.\n     * @private\n     * @return {*}  {go.ObjectData} An object containing properties and event handlers for Class elements\n     * @memberof GojsService\n     */\n    getClassOrAssociativeProperties() {\n      return {\n        ...this.getCommonGroupProperties(),\n        mouseEnter: (_e, node) => this.toggleSmallPortsVisibility(node, true),\n        mouseLeave: (_e, node) => this.toggleSmallPortsVisibility(node, false),\n        linkValidation: (fromNode, _fromPort, toNode, _toPort, link) => this.goJsCardinalityService.validateGroupLink(fromNode, toNode, link, this._gojsDiagram)\n      };\n    }\n    /**\n     * Returns an array of common two-way bindings used across different GoJS group templates.\n     * @private\n     * @return {*}  {go.Binding[]} An array of GoJS Binding objects configured for two-way data binding\n     * @memberof GojsService\n     */\n    getCommonBindings() {\n      return [new go.Binding('resizable', 'editable').makeTwoWay(),\n      // new go.Binding('id', 'id').makeTwoWay(),\n      new go.Binding('selectable', 'editable').makeTwoWay(), new go.Binding('handlesDragDropForMembers', 'editable').makeTwoWay(), new go.Binding('position', 'position', go.Point.parse).makeTwoWay(go.Point.stringify)];\n    }\n    /**\n     * Configures the palette with the provided templates.\n     * @private\n     * @param {go.Node} paletteTemplate - The node template for the palette.\n     * @param {go.Group} groupTemplate - The group template for the palette.\n     * @memberof DiagramEditorComponent\n     */\n    configureComponentPalette(paletteTemplate, groupTemplate) {\n      paletteConfigs.forEach(config => {\n        if (!go.Palette.fromDiv(config.name)) {\n          const palette = new go.Palette(config.name);\n          // Configure palette settings\n          palette.allowZoom = false;\n          palette.allowDrop = !this._gojsDiagram.toolManager.textEditingTool.isActive;\n          // Set the node template (left-aligned version)\n          palette.nodeTemplate = paletteTemplate;\n          // Set the group template\n          palette.groupTemplate = groupTemplate;\n          // Configure the palette layout as a grid\n          palette.layout = this.$(go.GridLayout, {\n            wrappingColumn: 2,\n            // spacing: new go.Size(10, 10), // Spacing between items\n            alignment: go.GridAlignment.Position // Align items to the grid positions\n          });\n          // Set the model\n          const model = new go.GraphLinksModel(config.data, []);\n          if (config.links) {\n            model.addLinkDataCollection(config.links);\n          }\n          palette.model = model;\n        }\n      });\n    }\n    static #_ = this.ɵfac = function GojsService_Factory(t) {\n      return new (t || GojsService)(i0.ɵɵinject(i1.AccessService), i0.ɵɵinject(i2.DiagramUtils), i0.ɵɵinject(i3.PropertyService), i0.ɵɵinject(i4.EventListenerService), i0.ɵɵinject(i5.GojsClassService), i0.ɵɵinject(i6.GojsEnumerationService), i0.ɵɵinject(i7.GojsLiteralService), i0.ɵɵinject(i8.GojsFolderService), i0.ɵɵinject(i9.GojsAttributeService), i0.ɵɵinject(i10.GojsCardinalityService), i0.ɵɵinject(i11.GojsCommonService), i0.ɵɵinject(i12.GojsCommentService), i0.ɵɵinject(i13.TreeNodeService), i0.ɵɵinject(i14.SnackBarService), i0.ɵɵinject(i15.AppService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GojsService,\n      factory: GojsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return GojsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}