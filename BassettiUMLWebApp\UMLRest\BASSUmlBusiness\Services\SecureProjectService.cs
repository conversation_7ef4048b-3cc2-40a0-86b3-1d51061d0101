using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Pagination;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.AspNetCore.Http;

namespace BASSUmlBusiness.Services
{
    /// <summary>
    /// Secure wrapper for ProjectService that automatically handles permission validation
    /// Uses HttpContext to get pre-validated user information from ProjectPermissionAttribute
    /// </summary>
    public class SecureProjectService : IProjectService
    {
        private readonly ProjectService _projectService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public SecureProjectService(ProjectService projectService, IHttpContextAccessor httpContextAccessor)
        {
            _projectService = projectService;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets validated user email from HttpContext (set by ProjectPermissionAttribute)
        /// </summary>
        private string GetValidatedUserEmail()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items.ContainsKey("ValidatedUserEmail") == true)
            {
                return httpContext.Items["ValidatedUserEmail"]?.ToString() ?? string.Empty;
            }
            
            // Fallback to extracting from claims if not pre-validated
            var user = httpContext?.User;
            return user?.GetEmployeeEmail() ?? user?.GetEmail() ?? string.Empty;
        }

        /// <summary>
        /// Checks if the current request has been pre-validated by ProjectPermissionAttribute
        /// </summary>
        private bool IsRequestPreValidated(int projectId, AccessType requiredAccess)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items.ContainsKey("ValidatedProjectId") == true &&
                httpContext?.Items.ContainsKey("ValidatedAccessType") == true)
            {
                var validatedProjectId = (int)(httpContext.Items["ValidatedProjectId"] ?? 0);
                var validatedAccessType = (AccessType)(httpContext.Items["ValidatedAccessType"] ?? AccessType.View);
                
                return validatedProjectId == projectId && HasSufficientAccess(validatedAccessType, requiredAccess);
            }
            
            return false;
        }

        /// <summary>
        /// Checks if the validated access type is sufficient for the required access
        /// </summary>
        private bool HasSufficientAccess(AccessType validatedAccess, AccessType requiredAccess)
        {
            // Admin has all permissions
            if (validatedAccess == AccessType.Admin) return true;
            
            // Edit access includes View permissions
            if (validatedAccess == AccessType.Edit && (requiredAccess == AccessType.Edit || requiredAccess == AccessType.View)) return true;
            
            // View access only for View requirements
            if (validatedAccess == AccessType.View && requiredAccess == AccessType.View) return true;
            
            return false;
        }

        // Implement IProjectService interface with automatic permission handling
        public IList<Project> GetAllProjectsByUser(string email, PaginationFilter paginationFilter, int currentPage = 1)
        {
            // This method already filters by user, so no additional permission check needed
            return _projectService.GetAllProjectsByUser(email, paginationFilter, currentPage);
        }

        public Project GetProject(int idProject)
        {
            // Basic project retrieval - no permission check needed for internal use
            return _projectService.GetProject(idProject);
        }

        public Project GetProjectWithDiagramsAndClasses(int idProject, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(idProject, AccessType.View))
            {
                email = GetValidatedUserEmail();
            }
            
            return _projectService.GetProjectWithDiagramsAndClasses(idProject, email);
        }

        public Project CreateProject(Project project, string email)
        {
            // Project creation doesn't require existing project permissions
            return _projectService.CreateProject(project, email);
        }

        public Project UpdateProject(Project project, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(project.Id, AccessType.Edit))
            {
                email = GetValidatedUserEmail();
            }
            
            return _projectService.UpdateProject(project, email);
        }

        public void DeleteProject(int idProject, string email)
        {
            // Check if request is pre-validated, otherwise use provided email
            if (IsRequestPreValidated(idProject, AccessType.Admin))
            {
                email = GetValidatedUserEmail();
            }
            
            _projectService.DeleteProject(idProject, email);
        }
    }
}
