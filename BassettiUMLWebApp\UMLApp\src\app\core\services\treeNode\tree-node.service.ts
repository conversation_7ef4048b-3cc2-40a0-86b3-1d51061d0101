import { computed, Injectable, signal } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AttributeMemberType } from 'src/app/shared/model/attribute';
import { FolderDTO, TemplateClass } from 'src/app/shared/model/class';
import { GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import { TemplateEnumeration } from 'src/app/shared/model/enumeration';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { ProjectDetails } from 'src/app/shared/model/project';
import { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';
import {
  ClassWrapperCategory,
  DiagramWrapperCategory,
  Diagram<PERSON>rapperName,
  EnumWrapperCategory,
} from 'src/app/shared/utils/constants';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { ClassService } from '../class/class.service';
import { DataFormatService } from '../data-format/data-format.service';
import { EnumerationService } from '../enumeration/enumeration.service';
import { FolderService } from '../folder/folder.service';

@Injectable({
  providedIn: 'root',
})
export class TreeNodeService {
  // Convert to signals
  private _libraryDetails = signal<TreeNode | null>(null);
  public readonly libraryDetails = this._libraryDetails.asReadonly();

  // Keep BehaviorSubject for backward compatibility during transition
  private libraryDetailsSubject = new BehaviorSubject<TreeNode | null>(null);

  private _descendantTreeNodes: TreeNode[] | null = [];
  private currentDiagramId: number = -1;

  // Computed signals for derived data
  public readonly descendantNodes = computed(() => {
    const root = this._libraryDetails();
    if (!root) return [];

    const nodes: TreeNode[] = [];
    const collectNodes = (nodesList: TreeNode[]) => {
      nodesList.forEach((node) => {
        nodes.push(node);
        if (node.children) {
          collectNodes(node.children);
        }
      });
    };
    collectNodes([root]);
    return nodes;
  });
  CATEGORY_PRIORITY = [
    DiagramWrapperCategory,
    ClassWrapperCategory,
    EnumWrapperCategory,
    GojsNodeCategory.Folder, // Always at the end
  ];
  constructor(
    private dataFormatService: DataFormatService,
    private _classService: ClassService,
    private _enumerationService: EnumerationService,
    private _folderService: FolderService,
    private _diagramUtils: DiagramUtils
  ) {
    this._diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram && diagram.id) this.currentDiagramId = diagram.id;
    });
  }
  getLibraryDetails(): Observable<TreeNode | null> {
    return this.libraryDetailsSubject.asObservable();
  }

  setLibraryDetails(projectDetails: ProjectDetails) {
    const formattedNode = this.formatTreeData(projectDetails);
    this._libraryDetails.set(formattedNode);
    this.libraryDetailsSubject.next(formattedNode);
    return;
  }

  // Helper methods for signal access
  private getLibraryDetailsValue(): TreeNode | null {
    return this._libraryDetails();
  }

  private updateLibraryDetails(treeNode: TreeNode | null): void {
    this._libraryDetails.set(treeNode);
    this.libraryDetailsSubject.next(treeNode);
  }

  get descendantTreeNodes(): TreeNode[] | null {
    return this._descendantTreeNodes;
  }

  set descendantTreeNodes(nodes: TreeNode[]) {
    this._descendantTreeNodes = nodes;
  }

  getWrapperParentTag(wrapperTag: string) {
    return `${wrapperTag}_${TreeNodeTag.Project}`;
  }

  formatTreeData(projectDetails: ProjectDetails): TreeNode {
    const treeNode = {
      name: projectDetails.name,
      category: GojsNodeCategory.Project,
      children: [
        ...(projectDetails.diagrams.length > 0
          ? [
              this.formatDiagramTreeNode(
                projectDetails.diagrams,
                TreeNodeTag.Project,
                projectDetails.id!
              ),
            ]
          : []),
        ...this.formatClassAndEnum(
          projectDetails.templateClasses,
          projectDetails.templateEnumerations,
          TreeNodeTag.Project
        ),
        ...this.formatFoldersRecursively(
          projectDetails.folders,
          TreeNodeTag.Project,
          projectDetails.id!
        ),
      ],
      tag: TreeNodeTag.Project,
      icon: GoJsNodeIcon.Project,
      supportingNodes: [
        GojsNodeCategory.Class,
        GojsNodeCategory.AssociativeClass,
        GojsNodeCategory.Enumeration,
        GojsNodeCategory.Folder,
        GojsNodeCategory.Diagram,
      ],
    };
    return treeNode;
  }

  private formatFoldersRecursively(
    folders: FolderDTO[],
    parentTag: string,
    projectId: number
  ): TreeNode[] {
    return folders.map((folder) => {
      const children: TreeNode[] = [
        ...this.formatClassAndEnum(
          folder.templateClasses!,
          folder.templateEnumerations!,
          `atTag${GojsNodeCategory.Folder}_${folder.id}`
        ),
        ...this.sortTreeNodeChildren(
          this.formatFoldersRecursively(
            folder.childFolders || [],
            `atTag${GojsNodeCategory.Folder}_${folder.id}`,
            projectId
          )
        ),
      ];

      // Add diagrams to children only if their length is greater than zero
      if (folder.diagrams.length > 0) {
        children.unshift(
          this.formatDiagramTreeNode(
            folder.diagrams,
            `atTag${GojsNodeCategory.Folder}_${folder.id}`,
            projectId
          )
        );
      }

      return {
        name: folder.name,
        children: children,
        category: GojsNodeCategory.Folder,
        icon: GoJsNodeIcon.Folder,
        data: this.dataFormatService.formatFolderData(folder),
        tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,
        parentTag: parentTag,
        isDraggable: true,
        supportingNodes: [
          GojsNodeCategory.Class,
          GojsNodeCategory.AssociativeClass,
          GojsNodeCategory.Enumeration,
          GojsNodeCategory.Folder,
          GojsNodeCategory.Diagram,
        ],
      };
    });
  }

  private formatClassAndEnum(
    templateClasses: TemplateClass[],
    templateEnumerations: TemplateEnumeration[],
    parentTag: string
  ): TreeNode[] {
    const treeNodes: TreeNode[] = [];
    if (templateClasses.length > 0) {
      treeNodes.push({
        name: 'Classes',
        children: this.sortTreeNodeChildren(
          templateClasses.map((tempClass) => ({
            name: tempClass.name,
            children: this.sortTreeNodeChildren(
              this.formatAttributeNode(tempClass)
            ),
            category: tempClass.isAssociative
              ? GojsNodeCategory.AssociativeClass
              : GojsNodeCategory.Class,
            tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,
            icon: tempClass.isAssociative
              ? GoJsNodeIcon.Associative
              : GoJsNodeIcon.Class,
            parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,
            data: this.dataFormatService.formatDiagramClassNode(
              tempClass,
              this.dataFormatService.formatAttributeData(
                tempClass.attributes || []
              )
            ),
            isDraggable: true,
            supportingNodes: [
              GojsNodeCategory.Operation,
              GojsNodeCategory.Attribute,
            ],
          }))
        ),
        supportingNodes: [
          GojsNodeCategory.Class,
          GojsNodeCategory.AssociativeClass,
        ],
        category: ClassWrapperCategory,
        tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,
        parentTag: parentTag,
        icon: GoJsNodeIcon.Class,
      });
    }
    if (templateEnumerations.length > 0) {
      treeNodes.push({
        name: 'Enumerations',
        children: this.sortTreeNodeChildren(
          this.getTemplateEnumsForTree(templateEnumerations, parentTag)
        ),
        category: EnumWrapperCategory,
        icon: GoJsNodeIcon.Enumeration,
        tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,
        parentTag: parentTag,
        supportingNodes: [GojsNodeCategory.Enumeration],
      });
    }
    return treeNodes;
  }

  private formatDiagramTreeNode(
    diagrams: Diagram[],
    parentTag: string,
    projectId: number
  ): TreeNode {
    return {
      name: DiagramWrapperName,
      children: diagrams.map((diagram) => ({
        name: diagram.name,
        children: [],
        category: GojsNodeCategory.Diagram,
        icon: GoJsNodeIcon.Diagram,
        tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,
        data: { ...diagram, idProject: projectId },
        parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,
        isDraggable: true,
      })),
      tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,
      parentTag: parentTag,
      category: DiagramWrapperCategory,
      icon: GoJsNodeIcon.Diagram,
      supportingNodes: [GojsNodeCategory.Diagram],
    };
  }

  private getTemplateEnumsForTree(
    templateEnumerations: TemplateEnumeration[],
    parentTag: string
  ): TreeNode[] {
    return templateEnumerations.map((tempEnum) => {
      // add AttributeTypes for each tempEnum
      this._diagramUtils.addAttributeTypes({
        id: tempEnum.id?.toString()!,
        name: tempEnum?.name,
        isEnumeration: true,
      });

      // Return the formatted tree node for each tempEnum
      return {
        name: tempEnum.name,
        children: this.formatLiteralTreeNode(tempEnum),
        category: GojsNodeCategory.Enumeration,
        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,
        parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,
        icon: GoJsNodeIcon.Enumeration,
        data: this.dataFormatService.formatDiagramEnumData(
          tempEnum,
          this.dataFormatService.formatLiteralData(
            tempEnum.enumerationLiterals || []
          )
        ),
        isDraggable: true,
        supportingNodes: [GojsNodeCategory.EnumerationLiteral],
      };
    });
  }

  private formatLiteralTreeNode(tempEnum: TemplateEnumeration): TreeNode[] {
    return (
      tempEnum.enumerationLiterals?.map((literal) => ({
        name: literal.name,
        children: [],
        category: GojsNodeCategory.EnumerationLiteral,
        icon: GoJsNodeIcon.EnumerationLiteral,
        tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,
        parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,
        supportingNodes: [],
        data: {
          ...this.dataFormatService.formatLiteralData([literal])[0],
          idTemplateEnumeration: tempEnum.id,
        },
      })) || []
    );
  }

  private formatAttributeNode(tempClass: TemplateClass): TreeNode[] {
    return tempClass.attributes.map((attr) => ({
      name: attr.name,
      children: [],
      category:
        attr.category == AttributeMemberType.attribute
          ? GojsNodeCategory.Attribute
          : GojsNodeCategory.Operation,
      icon:
        attr.category == AttributeMemberType.attribute
          ? GoJsNodeIcon.Attribute
          : GoJsNodeIcon.Operation,
      tag: `atTag${
        attr.category == AttributeMemberType.attribute
          ? GojsNodeCategory.Attribute
          : GojsNodeCategory.Operation
      }_${attr.id}`,
      parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,
      data: {
        ...this.dataFormatService.formatAttributeData([attr])[0],
        idTemplateClass: tempClass.id,
      },
      supportingNodes: [],
    }));
  }

  /**
   * Adds a group node to the tree structure by either appending it to a parent node
   * or creating a wrapper node if a suitable parent is not found. The method handles
   * nodes based on their category, organizing `Folder` nodes separately from other types.
   * After insertion, the tree nodes are sorted to maintain a defined order.
   * @param nodeData - The data for the node to be added to the tree.
   *                   It contains the node's details, such as category and parent tag.
   */
  addGroupNodeInTree(nodeData: TreeNode): void {
    const treeNode = this.getLibraryDetailsValue();
    if (!treeNode) return;
    const isProjectOrFolderNode =
      nodeData.category === GojsNodeCategory.Folder &&
      nodeData.parentTag === TreeNodeTag.Project;
    if (isProjectOrFolderNode) {
      this.addNodeToChildren(nodeData, treeNode.children);
    } else {
      const parentNode = this.findNodeByTag(nodeData.parentTag!);
      if (parentNode) {
        this.addNodeToParent(nodeData, parentNode);
      } else {
        // If parent not found, add to root
        this.getOrCreateWrapperNode(nodeData, treeNode);
      }
    }
    // Ensure the tree structure is sorted and updated
    treeNode.children = this.sortTreeNodes(treeNode.children);
    this.updateLibraryDetails(treeNode);
  }

  private addNodeToChildren(node: TreeNode, children: TreeNode[]): void {
    children.push(node);
    this.sortTreeNodeChildren(children);
  }

  private addNodeToParent(node: TreeNode, parentNode: TreeNode): void {
    if (parentNode.category === GojsNodeCategory.Folder) {
      if (node.category !== GojsNodeCategory.Folder) {
        this.getOrCreateWrapperNode(node, parentNode);
      } else {
        this.addNodeToChildren(node, parentNode.children);
      }
    } else {
      this.addNodeToChildren(node, parentNode.children);
    }
  }

  /**
   * Sorts an array of tree nodes based on the priority of their categories.
   * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined
   * categories are placed at the end of the list.
   *
   * @param nodes - An array of `TreeNode` objects to be sorted.
   * @returns A sorted array of `TreeNode` objects, ordered by their category priority.
   */
  private sortTreeNodes(nodes: TreeNode[]): TreeNode[] {
    return nodes.sort((a, b) => {
      // Get the priority index of each node's category
      const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);
      const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);

      // Place nodes with unknown categories at the end
      if (indexA === -1) return 1;
      if (indexB === -1) return -1;

      // Sort by priority index in ascending order
      return indexA - indexB;
    });
  }

  private sortTreeNodeChildren(nodes: TreeNode[]): TreeNode[] {
    return nodes.sort((a, b) =>
      a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })
    );
  }
  /**
   * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.
   * If the wrapper node already exists, the `targetedNode` is added to its children.
   * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`
   * is added as its child.
   *
   * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.
   * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.
   */
  private getOrCreateWrapperNode(
    targetedNode: TreeNode,
    parentNode: TreeNode
  ): void {
    // Attempt to find an existing wrapper node within the parent's children
    let wrapperNode = parentNode.children.find(
      (node) =>
        node.tag ==
        `${
          targetedNode.category == GojsNodeCategory.Class ||
          targetedNode.category == GojsNodeCategory.AssociativeClass
            ? TreeNodeTag.ClassWrapper
            : targetedNode.category === GojsNodeCategory.Diagram
            ? TreeNodeTag.DiagramWrapper
            : TreeNodeTag.EnumerationWrapper
        }_${targetedNode.parentTag}`
    );

    if (wrapperNode) {
      // If the wrapper node exists, add the targeted node as its child
      wrapperNode.children.push(targetedNode);
      this.sortTreeNodeChildren(wrapperNode.children);
    } else {
      // Create a new wrapper node if it doesn't exist
      wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);
      parentNode?.children.push({
        ...wrapperNode,
        children: this.sortTreeNodeChildren([
          ...wrapperNode.children,
          targetedNode,
        ]),
      });
      this.sortTreeNodes(parentNode.children);
    }
  }

  addItemNodeInParent(itemNode: TreeNode) {
    const treeNode = this.getLibraryDetailsValue();
    const parentNode = this.findNodeByTag(itemNode.parentTag!);
    if (parentNode) {
      (
        parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode
      ).items.push(
        itemNode.data as GojsDiagramAttributeNode | GojsDiagramLiteralNode
      );
      parentNode.children.push(itemNode);
      this.sortTreeNodeChildren(parentNode.children);
      this.updateLibraryDetails(treeNode);
    }
  }

  editGroupTreeNode(treeNode: TreeNode) {
    const updatedLibraryDetails = this.getLibraryDetailsValue();
    if (updatedLibraryDetails) {
      const parentNode = this.findParentNode(
        treeNode.tag,
        updatedLibraryDetails
      );
      if (parentNode) {
        const nodeToUpdate = parentNode?.children.find(
          (node) => node.tag === treeNode.tag
        );
        if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {
          nodeToUpdate.name = treeNode.data.name;
          if (
            nodeToUpdate.category === GojsNodeCategory.Attribute ||
            nodeToUpdate.category === GojsNodeCategory.Operation ||
            nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral
          ) {
            this.updateItemInClassOrEnum(
              parentNode,
              treeNode,
              updatedLibraryDetails
            );
            if (
              nodeToUpdate.category == GojsNodeCategory.Attribute ||
              nodeToUpdate.category === GojsNodeCategory.Operation
            ) {
              const itemNode = nodeToUpdate.data as GojsDiagramAttributeNode;
              // (nodeToUpdate.data as GojsDiagramAttributeNode).dataType = (
              //   treeNode.data as GojsDiagramAttributeNode
              // ).dataType;
              this.updateNodeData(nodeToUpdate.data, {
                name: itemNode.name,
                id: itemNode.id,
                description: itemNode.description,
                dataType: itemNode.dataType,
              });
            } else {
              const itemNode = nodeToUpdate.data as GojsDiagramLiteralNode;
              this.updateNodeData(nodeToUpdate.data, {
                name: itemNode.name,
                id: itemNode.id,
              });
            }
          } else {
            const classOrEnumNode = treeNode.data as
              | GojsDiagramClassNode
              | GojsDiagramEnumerationNode;
            this.updateNodeData(nodeToUpdate.data, {
              name: classOrEnumNode.name,
              id: classOrEnumNode.id,
              color: classOrEnumNode.color,
              description: classOrEnumNode.description,
              tag: classOrEnumNode.tag,
              volumetry: classOrEnumNode.volumetry,
              treeNodeTag: classOrEnumNode.treeNodeTag,
              position: classOrEnumNode.position,
              size: classOrEnumNode.size,
            });
          }
          this.sortTreeNodeChildren(parentNode?.children);
          this.sortTreeNodes(parentNode?.children);
          this.updateLibraryDetails(updatedLibraryDetails);
        }
      }
    }
  }

  private updateNodeData<T extends object>(
    nodeToUpdate: T,
    treeNodeData: Partial<T>
  ): void {
    Object.keys(treeNodeData).forEach((key) => {
      if (key in nodeToUpdate) {
        (nodeToUpdate as any)[key] = (treeNodeData as any)[key];
      }
    });
  }

  updateItemInClassOrEnum(
    groupNode: TreeNode,
    treeNode: TreeNode,
    libraryDetails: TreeNode
  ) {
    if (groupNode.data && treeNode.data) {
      (
        groupNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode
      ).items.forEach((item) => {
        if (item.id == treeNode.data?.id) {
          Object.assign(item, treeNode.data);
        }
      });
      this.updateLibraryDetails(libraryDetails);
    }
  }

  deleteGroupTreeNode(treeNode: TreeNode) {
    const updatedLibraryDetails = this.getLibraryDetailsValue();
    const parentNode = this.findParentNode(
      treeNode.tag,
      updatedLibraryDetails!
    );
    if (parentNode) {
      const index = parentNode?.children.findIndex(
        (node) => node.tag === treeNode.tag
      );
      if (
        treeNode.category === GojsNodeCategory.Attribute ||
        treeNode.category === GojsNodeCategory.Operation ||
        treeNode.category === GojsNodeCategory.EnumerationLiteral
      ) {
        (
          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode
        ).items = (
          parentNode.data as GojsDiagramClassNode | GojsDiagramEnumerationNode
        ).items.filter((item) => item.id !== treeNode.data?.id);
      }
      if (index > -1) {
        parentNode?.children.splice(index, 1);
        if (
          parentNode.children.length == 0 &&
          (parentNode.category == ClassWrapperCategory ||
            parentNode.category == EnumWrapperCategory ||
            parentNode.category == DiagramWrapperCategory)
        ) {
          const emptyWrapperParentNode = this.findParentNode(
            parentNode.tag,
            updatedLibraryDetails!
          );
          if (emptyWrapperParentNode) {
            emptyWrapperParentNode.children =
              emptyWrapperParentNode.children.filter(
                (child: TreeNode) => child.tag !== parentNode.tag
              );
          }
        }
        this.updateLibraryDetails(updatedLibraryDetails);
      }
    }
  }

  moveNode(targetFolder: TreeNode, draggedNode: TreeNode) {
    debugger;
    // Get the current value of library details
    const updatedLibraryDetails = this.getLibraryDetailsValue();
    // Find and remove the node from its current parent's children array
    const parentNode = this.findParentNode(
      draggedNode.tag,
      updatedLibraryDetails!
    );

    if (
      !this.checkDropValidation(
        targetFolder,
        draggedNode,
        parentNode,
        updatedLibraryDetails!
      )
    )
      return;

    if (parentNode) {
      parentNode.children = parentNode.children.filter(
        (child: TreeNode) => child.tag !== draggedNode.tag
      );
      if (
        parentNode.children.length == 0 &&
        (parentNode.category == ClassWrapperCategory ||
          parentNode.category == EnumWrapperCategory ||
          parentNode.category == DiagramWrapperCategory)
      ) {
        const emptyWrapperParentNode = this.findParentNode(
          parentNode.tag,
          updatedLibraryDetails!
        );
        if (emptyWrapperParentNode) {
          emptyWrapperParentNode.children =
            emptyWrapperParentNode.children.filter(
              (child: TreeNode) => child.tag !== parentNode.tag
            );
        }
      }
    }
    // Add the node to the target folder's children array

    if (
      draggedNode.category === GojsNodeCategory.Folder ||
      targetFolder.category == ClassWrapperCategory ||
      targetFolder.category == EnumWrapperCategory ||
      targetFolder.category == DiagramWrapperCategory
    ) {
      targetFolder.children.push({
        ...draggedNode,
        parentTag: targetFolder.tag,
      });
      this.sortTreeNodeChildren(targetFolder.children);
      this.sortTreeNodes(targetFolder.children);
      this.moveNodeToFolder(targetFolder, draggedNode);
    } else {
      const targetFolderNode = this.findNodeByTag(targetFolder.tag);
      if (targetFolderNode) {
        const wrapperNode = this.constructWrapperNode(
          draggedNode,
          targetFolderNode.tag
        );
        const targetedWrapperNode = targetFolderNode?.children.find(
          (node: TreeNode) => node.tag === wrapperNode.tag
        );
        this.moveNodeToFolder(targetFolderNode, draggedNode);
        if (targetedWrapperNode) {
          targetedWrapperNode.children.push({
            ...draggedNode,
            parentTag: targetedWrapperNode.tag,
          });

          this.sortTreeNodeChildren(targetedWrapperNode.children);
        } else {
          targetFolderNode.children.push({
            ...wrapperNode,
            children: [
              ...wrapperNode.children,
              { ...draggedNode, parentTag: wrapperNode.tag },
            ],
            parentTag: targetFolder.tag,
          });
          this.sortTreeNodes(targetFolderNode.children);
        }
      }
    }
    // Update the Subject with the modified library details
    this.updateLibraryDetails(updatedLibraryDetails);
  }

  private checkDropValidation(
    targetNode: TreeNode,
    draggedNode: TreeNode,
    parentNode: TreeNode | null,
    libraryDetails: TreeNode
  ): boolean {
    if (
      parentNode?.parentTag === libraryDetails?.tag &&
      targetNode.category === GojsNodeCategory.Project &&
      draggedNode.category !== GojsNodeCategory.Folder
    ) {
      return false;
    }
    if (
      draggedNode.tag === targetNode.tag ||
      draggedNode.tag == targetNode.parentTag ||
      draggedNode.parentTag == targetNode.tag
    )
      return false;

    if (targetNode.category === GojsNodeCategory.Diagram) return false;

    // Check if the current parent is the dragged node
    let currentParent = this.findParentNode(targetNode.tag, libraryDetails);
    if (
      (targetNode.category == ClassWrapperCategory ||
        targetNode.category == DiagramWrapperCategory ||
        targetNode.category == EnumWrapperCategory) &&
      currentParent?.category == GojsNodeCategory.Folder
    ) {
      return false;
    }
    while (currentParent) {
      if (currentParent.tag === draggedNode.tag) {
        return false; // Found an ancestor
      }
      currentParent = this.findParentNode(currentParent.tag, libraryDetails);
    }
    return true;
  }

  findParentNode(nodeTag: string, folder: TreeNode): TreeNode | null {
    if (folder.children) {
      for (let child of folder.children) {
        if (child.tag === nodeTag) {
          return folder;
        }
        const node = this.findParentNode(nodeTag, child);
        if (node) return node;
      }
    }
    return null;
  }

  findNodeByTag(tag: string): TreeNode | null {
    if (tag == TreeNodeTag.Project) {
      return this.getLibraryDetailsValue();
    }
    return this.descendantTreeNodes?.find((node) => node.tag == tag) || null;
  }

  private constructWrapperNode(
    draggedNode: TreeNode,
    parentTag: string
  ): TreeNode {
    const wrapperNodeName =
      draggedNode.category === GojsNodeCategory.Class ||
      draggedNode.category === GojsNodeCategory.AssociativeClass
        ? 'Classes'
        : draggedNode.category === GojsNodeCategory.Diagram
        ? 'Diagrams'
        : 'Enumerations';
    const wrapperNodeTag =
      draggedNode.category === GojsNodeCategory.Class ||
      draggedNode.category === GojsNodeCategory.AssociativeClass
        ? TreeNodeTag.ClassWrapper
        : draggedNode.category === GojsNodeCategory.Diagram
        ? TreeNodeTag.DiagramWrapper
        : TreeNodeTag.EnumerationWrapper;
    const wrapperNodeCategory =
      draggedNode.category === GojsNodeCategory.Class ||
      draggedNode.category === GojsNodeCategory.AssociativeClass
        ? ClassWrapperCategory
        : draggedNode.category === GojsNodeCategory.Diagram
        ? DiagramWrapperCategory
        : EnumWrapperCategory;
    const wrapperNodeIcon =
      draggedNode.category === GojsNodeCategory.Class ||
      draggedNode.category === GojsNodeCategory.AssociativeClass
        ? GoJsNodeIcon.Class
        : draggedNode.category === GojsNodeCategory.Diagram
        ? GoJsNodeIcon.Diagram
        : GoJsNodeIcon.Enumeration;
    return {
      name: wrapperNodeName,
      children: [],
      category: wrapperNodeCategory,
      tag: `${wrapperNodeTag}_${parentTag}`,
      parentTag: parentTag,
      icon: wrapperNodeIcon,
      supportingNodes: [draggedNode.category],
    };
  }

  private moveNodeToFolder(targetNode: TreeNode, draggedNode: TreeNode) {
    if (draggedNode.data) {
      if (targetNode.category === GojsNodeCategory.Folder) {
        if (
          draggedNode.category === GojsNodeCategory.Class ||
          draggedNode.category === GojsNodeCategory.AssociativeClass
        ) {
          this._classService
            .moveTempClassToFolder({
              id: (draggedNode?.data as GojsDiagramClassNode).idTemplateClass,
              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,
            })
            .subscribe();
        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {
          this._enumerationService
            .moveTempEnumToFolder({
              id: (draggedNode?.data as GojsDiagramEnumerationNode)
                .idTemplateEnumeration,
              idFolder: (targetNode?.data as GojsFolderNode).idFolder!,
            })
            .subscribe();
        } else if (draggedNode.category === GojsNodeCategory.Diagram) {
          this._folderService.moveDiagramToFolder({
            id: (draggedNode?.data as Diagram).id!,
            idFolder: (targetNode?.data as GojsFolderNode).idFolder!,
          });
        } else if (draggedNode.category === GojsNodeCategory.Folder) {
          this._folderService.moveFolderToFolder({
            id: (draggedNode?.data as GojsFolderNode).idFolder!,
            parentFolderId: (targetNode?.data as GojsFolderNode).idFolder!,
          });
        }
      } else {
        if (draggedNode.category === GojsNodeCategory.Class) {
          this._classService
            .removeTempClassFromFolder(
              (draggedNode?.data as GojsDiagramClassNode).idTemplateClass
            )
            .subscribe();
        } else if (draggedNode.category === GojsNodeCategory.Enumeration) {
          this._enumerationService
            .removeTempEnumFromFolder(
              (draggedNode?.data as GojsDiagramEnumerationNode)
                .idTemplateEnumeration
            )
            .subscribe();
        } else if (draggedNode.category === GojsNodeCategory.Diagram) {
          this._folderService.removeDiagramFromFolder(
            (draggedNode?.data as Diagram).id!
          );
        } else if (draggedNode.category === GojsNodeCategory.Folder) {
          this._folderService.removeFolderFromFolder(
            (draggedNode?.data as GojsFolderNode).idFolder!
          );
        }
      }
    }
  }

  getClassesEnumsFromFolder(folderNode: TreeNode): TreeNode[] {
    const nodes: TreeNode[] = [];
    // Recursive function to collect classes, enums, and folders from the target folder
    const collectChildNodes = (node: TreeNode) => {
      node.children.forEach((child) => {
        if (
          child.category === ClassWrapperCategory ||
          child.category === EnumWrapperCategory
        ) {
          nodes.push(...child.children);
        } else if (child.category === GojsNodeCategory.Folder) {
          collectChildNodes(child);
        }
      });
    };
    // Start collecting from the folder node
    collectChildNodes(folderNode);
    return nodes;
  }

  /**
   * Deletes a diagram node by tag.
   * @param {string} tag - Unique identifier for the node.
   * @memberof TreeNodeService
   * @returns {void}
   */
  deleteDiagram(tag: string): void {
    const node = this.findNodeByTag(tag);
    if (node) this.deleteGroupTreeNode(node);
  }

  nodeExistOrNot(parentTag: string, nodes: TreeNode[]): boolean {
    const libraryDetails = this.getLibraryDetailsValue();
    if (libraryDetails) {
      const parentNode = this.findParentNode(parentTag, libraryDetails);
      if (parentNode) {
        if (parentNode.category == GojsNodeCategory.Folder) {
          if (nodes.some((node) => node.tag === parentNode.tag)) return true;
          else return this.nodeExistOrNot(parentNode.tag, nodes);
        }
        return nodes.some((node) => node.tag === parentNode.tag);
      } else return false;
    } else return false;
  }

  findCurrentDiagramParentNode(): TreeNode | null {
    const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;
    const libraryDetails = this.getLibraryDetailsValue();
    if (libraryDetails) {
      const wrapperNode = this.findParentNode(
        currentDiagramTag,
        libraryDetails
      );
      if (wrapperNode) {
        return this.findNodeByTag(wrapperNode.parentTag!);
      } else return null;
    } else return null;
  }

  // expandNode(tag: string): void {
  //   const node = this.findNodeByTag(tag);
  //   if (node) {
  //     node.isExpanded = true;
  //     this.libraryDetails.next(this.libraryDetails.getValue());
  //   }
  // }
}
