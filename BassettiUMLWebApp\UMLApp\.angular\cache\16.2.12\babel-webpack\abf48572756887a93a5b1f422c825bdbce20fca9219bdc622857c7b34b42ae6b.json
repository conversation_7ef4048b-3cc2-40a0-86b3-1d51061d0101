{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../cardinality/cardinality.service\";\nexport let GojsCommonService = /*#__PURE__*/(() => {\n  class GojsCommonService {\n    constructor(cardinalityService) {\n      this.cardinalityService = cardinalityService;\n      this._gojsDiagramSubject = new BehaviorSubject(null);\n    }\n    /**\n     * Sets the GoJS diagram instance.\n     *\n     * This method emits the specified `go.Diagram` instance to the `_gojsDiagramSubject`,\n     * allowing it to be shared or accessed throughout the application.\n     *\n     * @param diagram - The `go.Diagram` instance to be set as the current diagram.\n     */\n    setGojsDiagram(diagram) {\n      this._gojsDiagramSubject.next(diagram);\n    }\n    /**\n     * Subscribes to changes in the GoJS diagram instance.\n     *\n     * This method provides an observable stream of `go.Diagram` values, allowing\n     * consumers to react to updates in the current GoJS diagram instance.\n     *\n     * @returns An `Observable` that emits the current `go.Diagram` instance, or `null` if none is set.\n     */\n    gojsDiagramChanges() {\n      return this._gojsDiagramSubject.asObservable();\n    }\n    /**\n     * Updates the opacity of an RGBA color.\n     *\n     * @private\n     * @param rgbaColor The original RGBA color string.\n     * @param newOpacity The desired opacity level as a number between 0 and 1.\n     * @returns {string} A new RGBA color string with the updated opacity.\n     *\n     * @memberOf DiagramEditorComponent\n     */\n    updateRGBAColorWithOpacity(rgbaColor, newOpacity) {\n      // Extract RGBA components\n      const rgbaValues = rgbaColor.match(/\\d+(\\.\\d+)?/g);\n      if (rgbaValues) {\n        const red = parseFloat(rgbaValues[0]);\n        const green = parseFloat(rgbaValues[1]);\n        const blue = parseFloat(rgbaValues[2]);\n        // Adjust the alpha value (opacity)\n        const adjustedAlpha = newOpacity;\n        // Update the RGBA color with the new opacity\n        const newRgbaColor = `rgba(${red}, ${green}, ${blue}, ${adjustedAlpha})`;\n        return newRgbaColor;\n      } else {\n        // Handle the case where rgbaValues is null\n        return rgbaColor; // Return the original color as fallback\n      }\n    }\n    isGojsDiagramClassNode(node) {\n      return node && node.category === GojsNodeCategory.Class || node.category === GojsNodeCategory.AssociativeClass && typeof node.idTemplateClass === 'number';\n    }\n    isGojsDiagramEnumerationNode(node) {\n      return node && node.category === GojsNodeCategory.Enumeration && typeof node.idTemplateEnumeration === 'number';\n    }\n    isGojsDiagramAttributeNode(node) {\n      return node && (node.category === GojsNodeCategory.Attribute || node.category === GojsNodeCategory.Operation);\n    }\n    isGojsDiagramLiteralNode(node) {\n      return node && node.category === GojsNodeCategory.EnumerationLiteral;\n    }\n    isGojsPaletteFolderNode(node) {\n      return node && node.category === GojsNodeCategory.Folder;\n    }\n    isGojsLinkNode(node) {\n      return node && node.category === GojsNodeCategory.Association;\n    }\n    /**\n     * Updates properties of items within node data objects in the GoJS model's nodeDataArray based on a condition.\n     *\n     * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\n     * @param {UpdateCondition} condition - A function to check if an item meets the update criteria.\n     * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n     */\n    updateNodeDataItemsProperties(model, condition, properties) {\n      model.nodeDataArray.forEach(nodeData => {\n        if (nodeData && nodeData['items']) {\n          nodeData['items'].forEach(item => {\n            if (condition(item)) {\n              for (const key in properties) {\n                if (properties.hasOwnProperty(key)) {\n                  model.commit(model => {\n                    model.set(item, key, properties[key]);\n                  }, null);\n                }\n              }\n            }\n          });\n        }\n      });\n    }\n    /**\n     * Updates properties of node data objects in the GoJS model's nodeDataArray based on a condition.\n     *\n     * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`).\n     * @param {NodeUpdateCondition} condition - A function to check if a node data object meets the update criteria.\n     * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n     */\n    updateNodeDataProperties(model, condition, properties) {\n      model.nodeDataArray.forEach(nodeData => {\n        if (condition(nodeData)) {\n          for (const key in properties) {\n            if (properties.hasOwnProperty(key)) {\n              model.commit(model => {\n                model.set(nodeData, key, properties[key]);\n              }, null);\n            }\n          }\n        }\n      });\n    }\n    /**\n     * Sets multiple properties on a GoJS model data object.\n     *\n     * @param {go.Model} model - The GoJS model instance (e.g., `go.GraphLinksModel`, `go.GraphObject`).\n     * @param {go.ObjectData} data - The data object (node or link data) to update.\n     * @param {UpdateProperties} properties - An object containing key-value pairs of properties to update.\n     */\n    setDataProperties(model, data, properties) {\n      Object.keys(properties).forEach(key => {\n        model.commit(m => {\n          m.set(data, key, properties[key]);\n        }, null);\n      });\n    }\n    /**\n     *Commit transaction For updating the diagram node data\n        * @param {*} propertyData\n     * @memberof DiagramEditorComponent\n     */\n    commitGroupNodeData(nodeData, properties, gojsDiagram) {\n      nodeData.forEach(node => {\n        this.setDataProperties(gojsDiagram.model, node, properties);\n      });\n    }\n    /**\n     * For removing the group node like class and enumeration node with children from palette and diagram\n     * @param {GojsNodeCategory} category - The Category of node\n     * @param {go.Diagram} goJsDiagram - Diagram\n     * @memberof GojsCommonService\n     */\n    removeGroupNodeWithItems(idTemplate, category, goJsDiagram) {\n      const diagramNodes = goJsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] === idTemplate && node['category'] === category || node['idTemplateEnumeration'] === idTemplate && node['category'] === category);\n      goJsDiagram.model.removeNodeDataCollection(diagramNodes);\n    }\n    removeNodeFromDiagram(goJsDiagram, condition) {\n      const model = goJsDiagram.model;\n      model.nodeDataArray.forEach(nodeData => {\n        if (condition(nodeData)) {\n          model.removeNodeData(nodeData);\n        }\n      });\n    }\n    removeAssociationLink(diagram, classData) {\n      const relatedLinkData = diagram.model.linkDataArray.filter(link => link['idSourceTempClass'] == classData.idTemplateClass || link['idDestinationTempClass'] == classData.idTemplateClass);\n      if (relatedLinkData) {\n        relatedLinkData.forEach(associationLink => {\n          this.removeLinkToLink(diagram, link => link.to === associationLink['labelKeys'][0] && link.category === GojsNodeCategory.LinkToLink);\n        });\n        diagram.model.removeLinkDataCollection(relatedLinkData);\n      }\n    }\n    removeLinkToLink(diagram, condition, idAssociativeClass) {\n      const model = diagram.model;\n      const linksToRemove = model.linkDataArray.find(condition);\n      if (linksToRemove) {\n        model.removeLinkData(linksToRemove);\n        this.cardinalityService.removeLinkToLink(linksToRemove['key']);\n      } else if (idAssociativeClass) {\n        this.cardinalityService.removeLinkToLinkByAssociativeCls(idAssociativeClass);\n      }\n    }\n    checkGroupNodeExist(gojsDiagram, nodeData) {\n      const existingNode = gojsDiagram.model.nodeDataArray.find(node => {\n        return node['treeNodeTag'] === nodeData.tag;\n      });\n      if (existingNode) {\n        // Select the node using the diagram's selection manager\n        const part = gojsDiagram.findPartForData(existingNode);\n        if (part) {\n          // gojsDiagram.clearSelection();\n          gojsDiagram.select(part); // select the part\n        }\n        return true;\n      } else if (nodeData.category === GojsNodeCategory.AssociativeClass) {\n        // If one link to link is already present, select it and restrict to drop another associative class which have link to link with the same link\n        const linkToLink = this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == nodeData.data.idTemplateClass);\n        if (linkToLink) {\n          const existingLinkToLink = gojsDiagram.model.linkDataArray.find(node => {\n            return node['category'] === GojsNodeCategory.LinkToLink && node['idLink'] === linkToLink.idLink;\n          });\n          if (existingLinkToLink) {\n            // Select the node using the diagram's selection manager\n            const part = gojsDiagram.findPartForData(existingLinkToLink);\n            if (part) {\n              gojsDiagram.clearSelection();\n              gojsDiagram.select(part); // select the part\n            }\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    selectMultipleGroupNodeExist(goJsDiagram, nodeData) {\n      const existingParts = [];\n      nodeData.forEach(data => {\n        const matchingNode = goJsDiagram.model.nodeDataArray.find(node => node['treeNodeTag'] === data.tag);\n        if (matchingNode) {\n          const part = goJsDiagram.findPartForData(matchingNode);\n          if (part) {\n            existingParts.push(part);\n          }\n        }\n      });\n      if (existingParts.length > 0) {\n        goJsDiagram.clearSelection(); // Clear any previous selection\n        goJsDiagram.selectCollection(existingParts); // Select all matched parts\n        goJsDiagram.commandHandler.scrollToPart(existingParts[0]); // Optional: bring first selected node into view\n      }\n    }\n    static #_ = this.ɵfac = function GojsCommonService_Factory(t) {\n      return new (t || GojsCommonService)(i0.ɵɵinject(i1.CardinalityService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GojsCommonService,\n      factory: GojsCommonService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return GojsCommonService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}