{"ast": null, "code": "import { computed, signal } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { AttributeMemberType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { ClassWrapperCategory, DiagramWrapperCategory, DiagramWrapperName, EnumWrapperCategory } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../data-format/data-format.service\";\nimport * as i2 from \"../class/class.service\";\nimport * as i3 from \"../enumeration/enumeration.service\";\nimport * as i4 from \"../folder/folder.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nexport let TreeNodeService = /*#__PURE__*/(() => {\n  class TreeNodeService {\n    constructor(dataFormatService, _classService, _enumerationService, _folderService, _diagramUtils) {\n      this.dataFormatService = dataFormatService;\n      this._classService = _classService;\n      this._enumerationService = _enumerationService;\n      this._folderService = _folderService;\n      this._diagramUtils = _diagramUtils;\n      // Convert to signals\n      this._libraryDetails = signal(null);\n      this.libraryDetails = this._libraryDetails.asReadonly();\n      // Keep BehaviorSubject for backward compatibility during transition\n      this.libraryDetailsSubject = new BehaviorSubject(null);\n      this._descendantTreeNodes = [];\n      this.currentDiagramId = -1;\n      // Computed signals for derived data\n      this.descendantNodes = computed(() => {\n        const root = this._libraryDetails();\n        if (!root) return [];\n        const nodes = [];\n        const collectNodes = nodesList => {\n          nodesList.forEach(node => {\n            nodes.push(node);\n            if (node.children) {\n              collectNodes(node.children);\n            }\n          });\n        };\n        collectNodes([root]);\n        return nodes;\n      });\n      this.CATEGORY_PRIORITY = [DiagramWrapperCategory, ClassWrapperCategory, EnumWrapperCategory, GojsNodeCategory.Folder // Always at the end\n      ];\n      this._diagramUtils.activeDiagramChanges().subscribe(diagram => {\n        if (diagram && diagram.id) this.currentDiagramId = diagram.id;\n      });\n    }\n    getLibraryDetails() {\n      return this.libraryDetailsSubject.asObservable();\n    }\n    setLibraryDetails(projectDetails) {\n      const formattedNode = this.formatTreeData(projectDetails);\n      this._libraryDetails.set(formattedNode);\n      this.libraryDetailsSubject.next(formattedNode);\n      return;\n    }\n    // Helper methods for signal access\n    getLibraryDetailsValue() {\n      return this._libraryDetails();\n    }\n    updateLibraryDetails(treeNode) {\n      this._libraryDetails.set(treeNode);\n      this.libraryDetailsSubject.next(treeNode);\n    }\n    get descendantTreeNodes() {\n      return this._descendantTreeNodes;\n    }\n    set descendantTreeNodes(nodes) {\n      this._descendantTreeNodes = nodes;\n    }\n    getWrapperParentTag(wrapperTag) {\n      return `${wrapperTag}_${TreeNodeTag.Project}`;\n    }\n    formatTreeData(projectDetails) {\n      const treeNode = {\n        name: projectDetails.name,\n        category: GojsNodeCategory.Project,\n        children: [...(projectDetails.diagrams.length > 0 ? [this.formatDiagramTreeNode(projectDetails.diagrams, TreeNodeTag.Project, projectDetails.id)] : []), ...this.formatClassAndEnum(projectDetails.templateClasses, projectDetails.templateEnumerations, TreeNodeTag.Project), ...this.formatFoldersRecursively(projectDetails.folders, TreeNodeTag.Project, projectDetails.id)],\n        tag: TreeNodeTag.Project,\n        icon: GoJsNodeIcon.Project,\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n      };\n      return treeNode;\n    }\n    formatFoldersRecursively(folders, parentTag, projectId) {\n      return folders.map(folder => {\n        const children = [...this.formatClassAndEnum(folder.templateClasses, folder.templateEnumerations, `atTag${GojsNodeCategory.Folder}_${folder.id}`), ...this.sortTreeNodeChildren(this.formatFoldersRecursively(folder.childFolders || [], `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId))];\n        // Add diagrams to children only if their length is greater than zero\n        if (folder.diagrams.length > 0) {\n          children.unshift(this.formatDiagramTreeNode(folder.diagrams, `atTag${GojsNodeCategory.Folder}_${folder.id}`, projectId));\n        }\n        return {\n          name: folder.name,\n          children: children,\n          category: GojsNodeCategory.Folder,\n          icon: GoJsNodeIcon.Folder,\n          data: this.dataFormatService.formatFolderData(folder),\n          tag: `atTag${GojsNodeCategory.Folder}_${folder.id}`,\n          parentTag: parentTag,\n          isDraggable: true,\n          supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n        };\n      });\n    }\n    formatClassAndEnum(templateClasses, templateEnumerations, parentTag) {\n      const treeNodes = [];\n      if (templateClasses.length > 0) {\n        treeNodes.push({\n          name: 'Classes',\n          children: this.sortTreeNodeChildren(templateClasses.map(tempClass => ({\n            name: tempClass.name,\n            children: this.sortTreeNodeChildren(this.formatAttributeNode(tempClass)),\n            category: tempClass.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n            tag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n            icon: tempClass.isAssociative ? GoJsNodeIcon.Associative : GoJsNodeIcon.Class,\n            parentTag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n            data: this.dataFormatService.formatDiagramClassNode(tempClass, this.dataFormatService.formatAttributeData(tempClass.attributes || [])),\n            isDraggable: true,\n            supportingNodes: [GojsNodeCategory.Operation, GojsNodeCategory.Attribute]\n          }))),\n          supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass],\n          category: ClassWrapperCategory,\n          tag: `${TreeNodeTag.ClassWrapper}_${parentTag}`,\n          parentTag: parentTag,\n          icon: GoJsNodeIcon.Class\n        });\n      }\n      if (templateEnumerations.length > 0) {\n        treeNodes.push({\n          name: 'Enumerations',\n          children: this.sortTreeNodeChildren(this.getTemplateEnumsForTree(templateEnumerations, parentTag)),\n          category: EnumWrapperCategory,\n          icon: GoJsNodeIcon.Enumeration,\n          tag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n          parentTag: parentTag,\n          supportingNodes: [GojsNodeCategory.Enumeration]\n        });\n      }\n      return treeNodes;\n    }\n    formatDiagramTreeNode(diagrams, parentTag, projectId) {\n      return {\n        name: DiagramWrapperName,\n        children: diagrams.map(diagram => ({\n          name: diagram.name,\n          children: [],\n          category: GojsNodeCategory.Diagram,\n          icon: GoJsNodeIcon.Diagram,\n          tag: `atTag${GojsNodeCategory.Diagram}_${diagram.id}`,\n          data: {\n            ...diagram,\n            idProject: projectId\n          },\n          parentTag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n          isDraggable: true\n        })),\n        tag: `${TreeNodeTag.DiagramWrapper}_${parentTag}`,\n        parentTag: parentTag,\n        category: DiagramWrapperCategory,\n        icon: GoJsNodeIcon.Diagram,\n        supportingNodes: [GojsNodeCategory.Diagram]\n      };\n    }\n    getTemplateEnumsForTree(templateEnumerations, parentTag) {\n      return templateEnumerations.map(tempEnum => {\n        // add AttributeTypes for each tempEnum\n        this._diagramUtils.addAttributeTypes({\n          id: tempEnum.id?.toString(),\n          name: tempEnum?.name,\n          isEnumeration: true\n        });\n        // Return the formatted tree node for each tempEnum\n        return {\n          name: tempEnum.name,\n          children: this.formatLiteralTreeNode(tempEnum),\n          category: GojsNodeCategory.Enumeration,\n          tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n          parentTag: `${TreeNodeTag.EnumerationWrapper}_${parentTag}`,\n          icon: GoJsNodeIcon.Enumeration,\n          data: this.dataFormatService.formatDiagramEnumData(tempEnum, this.dataFormatService.formatLiteralData(tempEnum.enumerationLiterals || [])),\n          isDraggable: true,\n          supportingNodes: [GojsNodeCategory.EnumerationLiteral]\n        };\n      });\n    }\n    formatLiteralTreeNode(tempEnum) {\n      return tempEnum.enumerationLiterals?.map(literal => ({\n        name: literal.name,\n        children: [],\n        category: GojsNodeCategory.EnumerationLiteral,\n        icon: GoJsNodeIcon.EnumerationLiteral,\n        tag: `atTag${GojsNodeCategory.EnumerationLiteral}_${literal.id}`,\n        parentTag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n        supportingNodes: [],\n        data: {\n          ...this.dataFormatService.formatLiteralData([literal])[0],\n          idTemplateEnumeration: tempEnum.id\n        }\n      })) || [];\n    }\n    formatAttributeNode(tempClass) {\n      return tempClass.attributes.map(attr => ({\n        name: attr.name,\n        children: [],\n        category: attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation,\n        icon: attr.category == AttributeMemberType.attribute ? GoJsNodeIcon.Attribute : GoJsNodeIcon.Operation,\n        tag: `atTag${attr.category == AttributeMemberType.attribute ? GojsNodeCategory.Attribute : GojsNodeCategory.Operation}_${attr.id}`,\n        parentTag: `atTag${GojsNodeCategory.Class}_${tempClass.id}`,\n        data: {\n          ...this.dataFormatService.formatAttributeData([attr])[0],\n          idTemplateClass: tempClass.id\n        },\n        supportingNodes: []\n      }));\n    }\n    /**\n     * Adds a group node to the tree structure by either appending it to a parent node\n     * or creating a wrapper node if a suitable parent is not found. The method handles\n     * nodes based on their category, organizing `Folder` nodes separately from other types.\n     * After insertion, the tree nodes are sorted to maintain a defined order.\n     * @param nodeData - The data for the node to be added to the tree.\n     *                   It contains the node's details, such as category and parent tag.\n     */\n    addGroupNodeInTree(nodeData) {\n      const treeNode = this.getLibraryDetailsValue();\n      if (!treeNode) return;\n      const isProjectOrFolderNode = nodeData.category === GojsNodeCategory.Folder && nodeData.parentTag === TreeNodeTag.Project;\n      if (isProjectOrFolderNode) {\n        this.addNodeToChildren(nodeData, treeNode.children);\n      } else {\n        const parentNode = this.findNodeByTag(nodeData.parentTag);\n        if (parentNode) {\n          this.addNodeToParent(nodeData, parentNode);\n        } else {\n          // If parent not found, add to root\n          this.getOrCreateWrapperNode(nodeData, treeNode);\n        }\n      }\n      // Ensure the tree structure is sorted and updated\n      treeNode.children = this.sortTreeNodes(treeNode.children);\n      this.updateLibraryDetails(treeNode);\n    }\n    addNodeToChildren(node, children) {\n      children.push(node);\n      this.sortTreeNodeChildren(children);\n    }\n    addNodeToParent(node, parentNode) {\n      if (parentNode.category === GojsNodeCategory.Folder) {\n        if (node.category !== GojsNodeCategory.Folder) {\n          this.getOrCreateWrapperNode(node, parentNode);\n        } else {\n          this.addNodeToChildren(node, parentNode.children);\n        }\n      } else {\n        this.addNodeToChildren(node, parentNode.children);\n      }\n    }\n    /**\n     * Sorts an array of tree nodes based on the priority of their categories.\n     * The priority order is defined in `CATEGORY_PRIORITY`. Nodes with undefined\n     * categories are placed at the end of the list.\n     *\n     * @param nodes - An array of `TreeNode` objects to be sorted.\n     * @returns A sorted array of `TreeNode` objects, ordered by their category priority.\n     */\n    sortTreeNodes(nodes) {\n      return nodes.sort((a, b) => {\n        // Get the priority index of each node's category\n        const indexA = this.CATEGORY_PRIORITY.indexOf(a.category);\n        const indexB = this.CATEGORY_PRIORITY.indexOf(b.category);\n        // Place nodes with unknown categories at the end\n        if (indexA === -1) return 1;\n        if (indexB === -1) return -1;\n        // Sort by priority index in ascending order\n        return indexA - indexB;\n      });\n    }\n    sortTreeNodeChildren(nodes) {\n      return nodes.sort((a, b) => a.name.localeCompare(b.name, undefined, {\n        sensitivity: 'base'\n      }));\n    }\n    /**\n     * Ensures that a wrapper node exists for the specified `targetedNode` within the `parentNode`.\n     * If the wrapper node already exists, the `targetedNode` is added to its children.\n     * Otherwise, a new wrapper node is created, added to the `parentNode`, and the `targetedNode`\n     * is added as its child.\n     *\n     * @param targetedNode - The `TreeNode` that needs to be added to a wrapper node.\n     * @param parentNode - The parent `TreeNode` under which the wrapper node will be created or updated.\n     */\n    getOrCreateWrapperNode(targetedNode, parentNode) {\n      // Attempt to find an existing wrapper node within the parent's children\n      let wrapperNode = parentNode.children.find(node => node.tag == `${targetedNode.category == GojsNodeCategory.Class || targetedNode.category == GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : targetedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper}_${targetedNode.parentTag}`);\n      if (wrapperNode) {\n        // If the wrapper node exists, add the targeted node as its child\n        wrapperNode.children.push(targetedNode);\n        this.sortTreeNodeChildren(wrapperNode.children);\n      } else {\n        // Create a new wrapper node if it doesn't exist\n        wrapperNode = this.constructWrapperNode(targetedNode, parentNode.tag);\n        parentNode?.children.push({\n          ...wrapperNode,\n          children: this.sortTreeNodeChildren([...wrapperNode.children, targetedNode])\n        });\n        this.sortTreeNodes(parentNode.children);\n      }\n    }\n    addItemNodeInParent(itemNode) {\n      const treeNode = this.getLibraryDetailsValue();\n      const parentNode = this.findNodeByTag(itemNode.parentTag);\n      if (parentNode) {\n        parentNode.data.items.push(itemNode.data);\n        parentNode.children.push(itemNode);\n        this.sortTreeNodeChildren(parentNode.children);\n        this.updateLibraryDetails(treeNode);\n      }\n    }\n    editGroupTreeNode(treeNode) {\n      const updatedLibraryDetails = this.getLibraryDetailsValue();\n      if (updatedLibraryDetails) {\n        const parentNode = this.findParentNode(treeNode.tag, updatedLibraryDetails);\n        if (parentNode) {\n          const nodeToUpdate = parentNode?.children.find(node => node.tag === treeNode.tag);\n          if (nodeToUpdate && nodeToUpdate.data && treeNode.data) {\n            nodeToUpdate.name = treeNode.data.name;\n            if (nodeToUpdate.category === GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation || nodeToUpdate.category === GojsNodeCategory.EnumerationLiteral) {\n              this.updateItemInClassOrEnum(parentNode, treeNode, updatedLibraryDetails);\n              if (nodeToUpdate.category == GojsNodeCategory.Attribute || nodeToUpdate.category === GojsNodeCategory.Operation) {\n                const itemNode = nodeToUpdate.data;\n                // (nodeToUpdate.data as GojsDiagramAttributeNode).dataType = (\n                //   treeNode.data as GojsDiagramAttributeNode\n                // ).dataType;\n                this.updateNodeData(nodeToUpdate.data, {\n                  name: itemNode.name,\n                  id: itemNode.id,\n                  description: itemNode.description,\n                  dataType: itemNode.dataType\n                });\n              } else {\n                const itemNode = nodeToUpdate.data;\n                this.updateNodeData(nodeToUpdate.data, {\n                  name: itemNode.name,\n                  id: itemNode.id\n                });\n              }\n            } else {\n              const classOrEnumNode = treeNode.data;\n              this.updateNodeData(nodeToUpdate.data, {\n                name: classOrEnumNode.name,\n                id: classOrEnumNode.id,\n                color: classOrEnumNode.color,\n                description: classOrEnumNode.description,\n                tag: classOrEnumNode.tag,\n                volumetry: classOrEnumNode.volumetry,\n                treeNodeTag: classOrEnumNode.treeNodeTag,\n                position: classOrEnumNode.position,\n                size: classOrEnumNode.size\n              });\n            }\n            this.sortTreeNodeChildren(parentNode?.children);\n            this.sortTreeNodes(parentNode?.children);\n            this.updateLibraryDetails(updatedLibraryDetails);\n          }\n        }\n      }\n    }\n    updateNodeData(nodeToUpdate, treeNodeData) {\n      Object.keys(treeNodeData).forEach(key => {\n        if (key in nodeToUpdate) {\n          nodeToUpdate[key] = treeNodeData[key];\n        }\n      });\n    }\n    updateItemInClassOrEnum(groupNode, treeNode, libraryDetails) {\n      if (groupNode.data && treeNode.data) {\n        groupNode.data.items.forEach(item => {\n          if (item.id == treeNode.data?.id) {\n            Object.assign(item, treeNode.data);\n          }\n        });\n        this.updateLibraryDetails(libraryDetails);\n      }\n    }\n    deleteGroupTreeNode(treeNode) {\n      const updatedLibraryDetails = this.getLibraryDetailsValue();\n      const parentNode = this.findParentNode(treeNode.tag, updatedLibraryDetails);\n      if (parentNode) {\n        const index = parentNode?.children.findIndex(node => node.tag === treeNode.tag);\n        if (treeNode.category === GojsNodeCategory.Attribute || treeNode.category === GojsNodeCategory.Operation || treeNode.category === GojsNodeCategory.EnumerationLiteral) {\n          parentNode.data.items = parentNode.data.items.filter(item => item.id !== treeNode.data?.id);\n        }\n        if (index > -1) {\n          parentNode?.children.splice(index, 1);\n          if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n            const emptyWrapperParentNode = this.findParentNode(parentNode.tag, updatedLibraryDetails);\n            if (emptyWrapperParentNode) {\n              emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n            }\n          }\n          this.updateLibraryDetails(updatedLibraryDetails);\n        }\n      }\n    }\n    moveNode(targetFolder, draggedNode) {\n      debugger;\n      // Get the current value of library details\n      const updatedLibraryDetails = this.getLibraryDetailsValue();\n      // Find and remove the node from its current parent's children array\n      const parentNode = this.findParentNode(draggedNode.tag, updatedLibraryDetails);\n      if (!this.checkDropValidation(targetFolder, draggedNode, parentNode, updatedLibraryDetails)) return;\n      if (parentNode) {\n        parentNode.children = parentNode.children.filter(child => child.tag !== draggedNode.tag);\n        if (parentNode.children.length == 0 && (parentNode.category == ClassWrapperCategory || parentNode.category == EnumWrapperCategory || parentNode.category == DiagramWrapperCategory)) {\n          const emptyWrapperParentNode = this.findParentNode(parentNode.tag, updatedLibraryDetails);\n          if (emptyWrapperParentNode) {\n            emptyWrapperParentNode.children = emptyWrapperParentNode.children.filter(child => child.tag !== parentNode.tag);\n          }\n        }\n      }\n      // Add the node to the target folder's children array\n      if (draggedNode.category === GojsNodeCategory.Folder || targetFolder.category == ClassWrapperCategory || targetFolder.category == EnumWrapperCategory || targetFolder.category == DiagramWrapperCategory) {\n        targetFolder.children.push({\n          ...draggedNode,\n          parentTag: targetFolder.tag\n        });\n        this.sortTreeNodeChildren(targetFolder.children);\n        this.sortTreeNodes(targetFolder.children);\n        this.moveNodeToFolder(targetFolder, draggedNode);\n      } else {\n        const targetFolderNode = this.findNodeByTag(targetFolder.tag);\n        if (targetFolderNode) {\n          const wrapperNode = this.constructWrapperNode(draggedNode, targetFolderNode.tag);\n          const targetedWrapperNode = targetFolderNode?.children.find(node => node.tag === wrapperNode.tag);\n          this.moveNodeToFolder(targetFolderNode, draggedNode);\n          if (targetedWrapperNode) {\n            targetedWrapperNode.children.push({\n              ...draggedNode,\n              parentTag: targetedWrapperNode.tag\n            });\n            this.sortTreeNodeChildren(targetedWrapperNode.children);\n          } else {\n            targetFolderNode.children.push({\n              ...wrapperNode,\n              children: [...wrapperNode.children, {\n                ...draggedNode,\n                parentTag: wrapperNode.tag\n              }],\n              parentTag: targetFolder.tag\n            });\n            this.sortTreeNodes(targetFolderNode.children);\n          }\n        }\n      }\n      // Update the Subject with the modified library details\n      this.updateLibraryDetails(updatedLibraryDetails);\n    }\n    checkDropValidation(targetNode, draggedNode, parentNode, libraryDetails) {\n      if (parentNode?.parentTag === libraryDetails?.tag && targetNode.category === GojsNodeCategory.Project && draggedNode.category !== GojsNodeCategory.Folder) {\n        return false;\n      }\n      if (draggedNode.tag === targetNode.tag || draggedNode.tag == targetNode.parentTag || draggedNode.parentTag == targetNode.tag) return false;\n      if (targetNode.category === GojsNodeCategory.Diagram) return false;\n      // Check if the current parent is the dragged node\n      let currentParent = this.findParentNode(targetNode.tag, libraryDetails);\n      if ((targetNode.category == ClassWrapperCategory || targetNode.category == DiagramWrapperCategory || targetNode.category == EnumWrapperCategory) && currentParent?.category == GojsNodeCategory.Folder) {\n        return false;\n      }\n      while (currentParent) {\n        if (currentParent.tag === draggedNode.tag) {\n          return false; // Found an ancestor\n        }\n        currentParent = this.findParentNode(currentParent.tag, libraryDetails);\n      }\n      return true;\n    }\n    findParentNode(nodeTag, folder) {\n      if (folder.children) {\n        for (let child of folder.children) {\n          if (child.tag === nodeTag) {\n            return folder;\n          }\n          const node = this.findParentNode(nodeTag, child);\n          if (node) return node;\n        }\n      }\n      return null;\n    }\n    findNodeByTag(tag) {\n      if (tag == TreeNodeTag.Project) {\n        return this.getLibraryDetailsValue();\n      }\n      return this.descendantTreeNodes?.find(node => node.tag == tag) || null;\n    }\n    constructWrapperNode(draggedNode, parentTag) {\n      const wrapperNodeName = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? 'Classes' : draggedNode.category === GojsNodeCategory.Diagram ? 'Diagrams' : 'Enumerations';\n      const wrapperNodeTag = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? TreeNodeTag.ClassWrapper : draggedNode.category === GojsNodeCategory.Diagram ? TreeNodeTag.DiagramWrapper : TreeNodeTag.EnumerationWrapper;\n      const wrapperNodeCategory = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? ClassWrapperCategory : draggedNode.category === GojsNodeCategory.Diagram ? DiagramWrapperCategory : EnumWrapperCategory;\n      const wrapperNodeIcon = draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass ? GoJsNodeIcon.Class : draggedNode.category === GojsNodeCategory.Diagram ? GoJsNodeIcon.Diagram : GoJsNodeIcon.Enumeration;\n      return {\n        name: wrapperNodeName,\n        children: [],\n        category: wrapperNodeCategory,\n        tag: `${wrapperNodeTag}_${parentTag}`,\n        parentTag: parentTag,\n        icon: wrapperNodeIcon,\n        supportingNodes: [draggedNode.category]\n      };\n    }\n    moveNodeToFolder(targetNode, draggedNode) {\n      if (draggedNode.data) {\n        if (targetNode.category === GojsNodeCategory.Folder) {\n          if (draggedNode.category === GojsNodeCategory.Class || draggedNode.category === GojsNodeCategory.AssociativeClass) {\n            this._classService.moveTempClassToFolder({\n              id: (draggedNode?.data).idTemplateClass,\n              idFolder: (targetNode?.data).idFolder\n            }).subscribe();\n          } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n            this._enumerationService.moveTempEnumToFolder({\n              id: (draggedNode?.data).idTemplateEnumeration,\n              idFolder: (targetNode?.data).idFolder\n            }).subscribe();\n          } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n            this._folderService.moveDiagramToFolder({\n              id: (draggedNode?.data).id,\n              idFolder: (targetNode?.data).idFolder\n            });\n          } else if (draggedNode.category === GojsNodeCategory.Folder) {\n            this._folderService.moveFolderToFolder({\n              id: (draggedNode?.data).idFolder,\n              parentFolderId: (targetNode?.data).idFolder\n            });\n          }\n        } else {\n          if (draggedNode.category === GojsNodeCategory.Class) {\n            this._classService.removeTempClassFromFolder((draggedNode?.data).idTemplateClass).subscribe();\n          } else if (draggedNode.category === GojsNodeCategory.Enumeration) {\n            this._enumerationService.removeTempEnumFromFolder((draggedNode?.data).idTemplateEnumeration).subscribe();\n          } else if (draggedNode.category === GojsNodeCategory.Diagram) {\n            this._folderService.removeDiagramFromFolder((draggedNode?.data).id);\n          } else if (draggedNode.category === GojsNodeCategory.Folder) {\n            this._folderService.removeFolderFromFolder((draggedNode?.data).idFolder);\n          }\n        }\n      }\n    }\n    getClassesEnumsFromFolder(folderNode) {\n      const nodes = [];\n      // Recursive function to collect classes, enums, and folders from the target folder\n      const collectChildNodes = node => {\n        node.children.forEach(child => {\n          if (child.category === ClassWrapperCategory || child.category === EnumWrapperCategory) {\n            nodes.push(...child.children);\n          } else if (child.category === GojsNodeCategory.Folder) {\n            collectChildNodes(child);\n          }\n        });\n      };\n      // Start collecting from the folder node\n      collectChildNodes(folderNode);\n      return nodes;\n    }\n    /**\n     * Deletes a diagram node by tag.\n     * @param {string} tag - Unique identifier for the node.\n     * @memberof TreeNodeService\n     * @returns {void}\n     */\n    deleteDiagram(tag) {\n      const node = this.findNodeByTag(tag);\n      if (node) this.deleteGroupTreeNode(node);\n    }\n    nodeExistOrNot(parentTag, nodes) {\n      const libraryDetails = this.getLibraryDetailsValue();\n      if (libraryDetails) {\n        const parentNode = this.findParentNode(parentTag, libraryDetails);\n        if (parentNode) {\n          if (parentNode.category == GojsNodeCategory.Folder) {\n            if (nodes.some(node => node.tag === parentNode.tag)) return true;else return this.nodeExistOrNot(parentNode.tag, nodes);\n          }\n          return nodes.some(node => node.tag === parentNode.tag);\n        } else return false;\n      } else return false;\n    }\n    findCurrentDiagramParentNode() {\n      const currentDiagramTag = `atTag${GojsNodeCategory.Diagram}_${this.currentDiagramId}`;\n      const libraryDetails = this.getLibraryDetailsValue();\n      if (libraryDetails) {\n        const wrapperNode = this.findParentNode(currentDiagramTag, libraryDetails);\n        if (wrapperNode) {\n          return this.findNodeByTag(wrapperNode.parentTag);\n        } else return null;\n      } else return null;\n    }\n    static #_ = this.ɵfac = function TreeNodeService_Factory(t) {\n      return new (t || TreeNodeService)(i0.ɵɵinject(i1.DataFormatService), i0.ɵɵinject(i2.ClassService), i0.ɵɵinject(i3.EnumerationService), i0.ɵɵinject(i4.FolderService), i0.ɵɵinject(i5.DiagramUtils));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TreeNodeService,\n      factory: TreeNodeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TreeNodeService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}