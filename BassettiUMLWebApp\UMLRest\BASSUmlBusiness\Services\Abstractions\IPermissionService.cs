﻿using BASSUmlBusiness.BusinessModel;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;

namespace BASSUmlBusiness.Services.Abstractions
{
    public interface IPermissionService
    {
        /// <summary>
        /// Create a new permission.
        /// </summary>
        /// <param name="permission">The permission to be created.</param>
        /// <returns>Newly created permission</returns>
        Permission CreatePermission(Permission permission);

        /// <summary>
        /// Create permission for multiple user at once
        /// </summary>
        /// <param name="permission">Require a <see cref="AddPermission">Permission</see> object</param>
        /// <param name="email">Email of the login user for owner validation</param>
        /// <returns>Newly created permissions</returns>
        IList<Permission> AddPermissions(AddPermission permission, string email);

        /// <summary>
        /// Delete a permission depending on the permission id.
        /// </summary>
        /// <param name="idPermission"></param>
        /// <param name="email">Email of the login user for owner validation</param>
        void DeletePermission(int idPermission, string email);

        /// <summary>
        /// Get all permissions of a specific project
        /// </summary>
        /// <param name="idProject">The ID of the project.</param>
        /// <returns>List of Permissions</returns>
        IList<Permission> GetPermissions(int idProject);

        /// <summary>
        /// Get permission of a user for a project
        /// </summary>
        /// <param name="idProject">Identifier of the project</param>
        /// <param name="email">User email id</param>
        /// <returns>Permission</returns>
        Permission GetProjectPermissionByUser(int idProject, string email);

        /// <summary>
        /// Update list of permission if found in database except the Admin
        /// </summary>
        /// <param name="permissions">List of permision with update details</param>
        /// <param name="email">Email of the login user for owner validation</param>
        /// <returns>Updated permission list</returns>
        List<Permission> UpdatePermission(List<Permission> permissions, string email);

        /// <summary>
        /// Validates if a user has the required permission level for a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <param name="requiredAccessType">Minimum required access type</param>
        /// <returns>True if user has required permission, false otherwise</returns>
        bool ValidateUserPermission(string email, int idProject, AccessType requiredAccessType);

        /// <summary>
        /// Validates if a user has edit or admin access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user can edit the project</returns>
        bool CanUserEditProject(string email, int idProject);

        /// <summary>
        /// Validates if a user has admin access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user is admin of the project</returns>
        bool IsUserProjectAdmin(string email, int idProject);

        /// <summary>
        /// Validates if a user has at least view access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user can view the project</returns>
        bool CanUserViewProject(string email, int idProject);

        /// <summary>
        /// Validates user permission and throws SecurityException if insufficient
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <param name="requiredAccessType">Required access type</param>
        /// <exception cref="SecurityException">Thrown when user lacks required permission</exception>
        void ValidateUserPermissionOrThrow(string email, int idProject, AccessType requiredAccessType);
    }
}
