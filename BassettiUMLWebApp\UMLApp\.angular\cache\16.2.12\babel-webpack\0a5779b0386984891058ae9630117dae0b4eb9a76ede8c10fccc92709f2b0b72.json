{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { catchError, map, of } from 'rxjs';\nimport { PermissionApiService } from '../services/api/permission-api.service';\nimport { ErrorService } from '../services/errors/error.service';\nexport const accessGuard = (route, state) => {\n  const permissionApiService = inject(PermissionApiService);\n  const router = inject(Router);\n  const errorService = inject(ErrorService);\n  const navigation = history.state?.navigationId;\n  if (!navigation) {\n    localStorage.setItem('copyUrl', 'true');\n  }\n  // Extract project ID from route parameters\n  const projectId = route.params['id'];\n  if (!projectId) {\n    // If no project ID, allow access (for dashboard, etc.)\n    return true;\n  }\n  // Check user permission for the project\n  return permissionApiService.checkPermission(Number(projectId)).pipe(map(permission => {\n    // If user has any permission (View, Edit, or Admin), allow access\n    if (permission && (permission.accessType === 0 || permission.accessType === 1 || permission.accessType === 2)) {\n      return true;\n    } else {\n      // User doesn't have permission, redirect to dashboard\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 403,\n        type: 'error',\n        header: 'Access Denied',\n        content: 'You do not have permission to access this project.',\n        isCustomError: true\n      });\n      return false;\n    }\n  }), catchError(error => {\n    // Handle permission check error\n    console.error('Error checking project permission:', error);\n    if (error.status === 404) {\n      // Project not found or user has no permission\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 404,\n        type: 'error',\n        header: 'Project Not Found',\n        content: 'The requested project was not found or you do not have access to it.',\n        isCustomError: true\n      });\n    } else {\n      // Other errors\n      router.navigate(['/dashboard']);\n      errorService.addError({\n        errorKey: 500,\n        type: 'error',\n        header: 'Access Check Failed',\n        content: 'Unable to verify project access. Please try again.',\n        isCustomError: true\n      });\n    }\n    return of(false);\n  }));\n};", "map": {"version": 3, "names": ["inject", "Router", "catchError", "map", "of", "PermissionApiService", "ErrorService", "accessGuard", "route", "state", "permissionApiService", "router", "errorService", "navigation", "history", "navigationId", "localStorage", "setItem", "projectId", "params", "checkPermission", "Number", "pipe", "permission", "accessType", "navigate", "addError", "<PERSON><PERSON><PERSON>", "type", "header", "content", "isCustomError", "error", "console", "status"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\guards\\access.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { catchError, map, of } from 'rxjs';\r\nimport { PermissionApiService } from '../services/api/permission-api.service';\r\nimport { ErrorService } from '../services/errors/error.service';\r\n\r\nexport const accessGuard: CanActivateFn = (route, state) => {\r\n  const permissionApiService = inject(PermissionApiService);\r\n  const router = inject(Router);\r\n  const errorService = inject(ErrorService);\r\n\r\n  const navigation = history.state?.navigationId;\r\n  if (!navigation) {\r\n    localStorage.setItem('copyUrl', 'true');\r\n  }\r\n\r\n  // Extract project ID from route parameters\r\n  const projectId = route.params['id'];\r\n\r\n  if (!projectId) {\r\n    // If no project ID, allow access (for dashboard, etc.)\r\n    return true;\r\n  }\r\n\r\n  // Check user permission for the project\r\n  return permissionApiService.checkPermission(Number(projectId)).pipe(\r\n    map((permission) => {\r\n      // If user has any permission (View, Edit, or Admin), allow access\r\n      if (\r\n        permission &&\r\n        (permission.accessType === 0 ||\r\n          permission.accessType === 1 ||\r\n          permission.accessType === 2)\r\n      ) {\r\n        return true;\r\n      } else {\r\n        // User doesn't have permission, redirect to dashboard\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 403,\r\n          type: 'error',\r\n          header: 'Access Denied',\r\n          content: 'You do not have permission to access this project.',\r\n          isCustomError: true,\r\n        });\r\n        return false;\r\n      }\r\n    }),\r\n    catchError((error) => {\r\n      // Handle permission check error\r\n      console.error('Error checking project permission:', error);\r\n\r\n      if (error.status === 404) {\r\n        // Project not found or user has no permission\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 404,\r\n          type: 'error',\r\n          header: 'Project Not Found',\r\n          content:\r\n            'The requested project was not found or you do not have access to it.',\r\n          isCustomError: true,\r\n        });\r\n      } else {\r\n        // Other errors\r\n        router.navigate(['/dashboard']);\r\n        errorService.addError({\r\n          errorKey: 500,\r\n          type: 'error',\r\n          header: 'Access Check Failed',\r\n          content: 'Unable to verify project access. Please try again.',\r\n          isCustomError: true,\r\n        });\r\n      }\r\n\r\n      return of(false);\r\n    })\r\n  );\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,UAAU,EAAEC,GAAG,EAAEC,EAAE,QAAQ,MAAM;AAC1C,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,YAAY,QAAQ,kCAAkC;AAE/D,OAAO,MAAMC,WAAW,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACzD,MAAMC,oBAAoB,GAAGV,MAAM,CAACK,oBAAoB,CAAC;EACzD,MAAMM,MAAM,GAAGX,MAAM,CAACC,MAAM,CAAC;EAC7B,MAAMW,YAAY,GAAGZ,MAAM,CAACM,YAAY,CAAC;EAEzC,MAAMO,UAAU,GAAGC,OAAO,CAACL,KAAK,EAAEM,YAAY;EAC9C,IAAI,CAACF,UAAU,EAAE;IACfG,YAAY,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;;EAGzC;EACA,MAAMC,SAAS,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAI,CAAC;EAEpC,IAAI,CAACD,SAAS,EAAE;IACd;IACA,OAAO,IAAI;;EAGb;EACA,OAAOR,oBAAoB,CAACU,eAAe,CAACC,MAAM,CAACH,SAAS,CAAC,CAAC,CAACI,IAAI,CACjEnB,GAAG,CAAEoB,UAAU,IAAI;IACjB;IACA,IACEA,UAAU,KACTA,UAAU,CAACC,UAAU,KAAK,CAAC,IAC1BD,UAAU,CAACC,UAAU,KAAK,CAAC,IAC3BD,UAAU,CAACC,UAAU,KAAK,CAAC,CAAC,EAC9B;MACA,OAAO,IAAI;KACZ,MAAM;MACL;MACAb,MAAM,CAACc,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/Bb,YAAY,CAACc,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,eAAe;QACvBC,OAAO,EAAE,oDAAoD;QAC7DC,aAAa,EAAE;OAChB,CAAC;MACF,OAAO,KAAK;;EAEhB,CAAC,CAAC,EACF7B,UAAU,CAAE8B,KAAK,IAAI;IACnB;IACAC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAE1D,IAAIA,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxB;MACAvB,MAAM,CAACc,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/Bb,YAAY,CAACc,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EACL,sEAAsE;QACxEC,aAAa,EAAE;OAChB,CAAC;KACH,MAAM;MACL;MACApB,MAAM,CAACc,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAC/Bb,YAAY,CAACc,QAAQ,CAAC;QACpBC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE,qBAAqB;QAC7BC,OAAO,EAAE,oDAAoD;QAC7DC,aAAa,EAAE;OAChB,CAAC;;IAGJ,OAAO3B,EAAE,CAAC,KAAK,CAAC;EAClB,CAAC,CAAC,CACH;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}