﻿using BASSUmlBusiness.BusinessModel;
using BASSUmlBusiness.Exceptions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Pagination;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security;

namespace BASSUmlBusiness.Services
{
    public class ProjectService : IProjectService
    {
        private readonly UmlDbContext _context;
        private readonly IPermissionService _permissionService;
        private readonly IContactService _contactService;
        public ProjectService(UmlDbContext context, IPermissionService permissionService, IContactService contactService)
        {
            _context = context;
            _permissionService = permissionService;
            _contactService = contactService;
        }

        public IList<Project> GetAllProjectsByUser(string email, PaginationFilter paginationFilter, int currentPage = 1)
        {
            // Start with the base query
            var query = _context.Permissions
                .Where(permission => permission.Email == email)
                .Select(permission => new ProjectDetails
                {
                    Project = new Project
                    {
                        Id = permission.Project.Id,
                        Name = permission.Project.Name,
                        Description = permission.Project.Description,
                        ProductLine = permission.Project.ProductLine,
                        Type = permission.Project.Type,
                        LastModifiedDate = permission.Project.LastModifiedDate
                    },
                    AccessType = permission.AccessType,
                    LockInfo = _context.ProjectLocks.FirstOrDefault(pl => pl.IdProject == (permission.Project != null ? permission.Project.Id : 0)),
                    OwnerEmail = _context.Permissions
                        .Where(p => p.IdProject == (permission.Project != null ? permission.Project.Id : 0) && p.AccessType == AccessType.Admin)
                        .Select(p => p.Email)
                        .FirstOrDefault()
                });

            // Apply filtering if provided
            if (!string.IsNullOrEmpty(paginationFilter.SearchTerm))
            {
                string searchTerm = paginationFilter.SearchTerm.ToLower();

                // Get all contacts for owner name lookup
                Dictionary<string, string> contactsForSearch = _contactService.GetAllContacts()
                    .ToDictionary(contact => contact.Email, contact => contact.Name.ToLower());

                // Create a list of owner emails whose names match the search term
                List<string> matchingOwnerEmails = contactsForSearch
                    .Where(kvp => kvp.Value.Contains(searchTerm))
                    .Select(kvp => kvp.Key)
                    .ToList();

                // Execute the query to get the projects with details
                var allProjects = query.ToList();

                // Filter projects based on search term
                var filteredProjects = allProjects.Where(p =>
                    p.Project.Name.ToLower().Contains(searchTerm) ||
                    (p.Project.Description != null && p.Project.Description.ToLower().Contains(searchTerm)) ||
                    (p.Project.Type != null && p.Project.Type.ToLower().Contains(searchTerm)) ||
                    (p.Project.ProductLine != null && p.Project.ProductLine.ToLower().Contains(searchTerm)) ||
                    (p.OwnerEmail != null && p.OwnerEmail.ToLower().Contains(searchTerm)) ||
                    (p.OwnerEmail != null && matchingOwnerEmails.Contains(p.OwnerEmail))).ToList();

                // Return the filtered query
                query = filteredProjects.AsQueryable();
            }

            if (paginationFilter.ProjectTypes != null && paginationFilter.ProjectTypes.Any())
            {
                query = query.Where(p => paginationFilter.ProjectTypes.Contains(p.Project.Type));
            }

            if (paginationFilter.ProductLines != null && paginationFilter.ProductLines.Any())
            {
                query = query.Where(p => paginationFilter.ProductLines.Contains(p.Project.ProductLine));
            }

            // Apply sorting
            if (!string.IsNullOrEmpty(paginationFilter.SortBy))
            {
                switch (paginationFilter.SortBy.ToLower())
                {
                    case "name":
                        query = paginationFilter.SortDescending
                            ? query.OrderByDescending(p => p.Project.Name)
                            : query.OrderBy(p => p.Project.Name);
                        break;
                    case "description":
                        query = paginationFilter.SortDescending
                            ? query.OrderByDescending(p => p.Project.Description)
                            : query.OrderBy(p => p.Project.Description);
                        break;
                    case "type":
                        query = paginationFilter.SortDescending
                            ? query.OrderByDescending(p => p.Project.Type)
                            : query.OrderBy(p => p.Project.Type);
                        break;
                    case "productline":
                        query = paginationFilter.SortDescending
                            ? query.OrderByDescending(p => p.Project.ProductLine)
                            : query.OrderBy(p => p.Project.ProductLine);
                        break;
                    case "admin":
                    case "owner":
                        // Get all contacts for owner name lookup
                        Dictionary<string, string> contactsDict = _contactService.GetAllContacts()
                            .ToDictionary(contact => contact.Email, contact => contact.Name);

                        // Execute the query to get the projects with details
                        var ownerSortedProjects = query.ToList();

                        // Map owner emails to names and sort
                        var projectsWithOwnerNames = ownerSortedProjects
                            .Select(p => new
                            {
                                ProjectDetails = p,
                                OwnerName = p.OwnerEmail != null && contactsDict.TryGetValue(p.OwnerEmail, out string? ownerName) ? ownerName : string.Empty
                            });

                        // Apply sorting
                        projectsWithOwnerNames = paginationFilter.SortDescending
                            ? projectsWithOwnerNames.OrderByDescending(p => p.OwnerName)
                            : projectsWithOwnerNames.OrderBy(p => p.OwnerName);

                        // Convert back to the original query format
                        ownerSortedProjects = projectsWithOwnerNames.Select(p => p.ProjectDetails).ToList();

                        // Return the sorted query
                        query = ownerSortedProjects.AsQueryable();
                        break;
                    case "lastmodifieddate":
                    default:
                        query = paginationFilter.SortDescending
                            ? query.OrderByDescending(p => p.Project.LastModifiedDate)
                            : query.OrderBy(p => p.Project.LastModifiedDate);
                        break;
                }
            }
            else
            {
                // Default sorting
                query = query.OrderByDescending(p => p.Project.LastModifiedDate);
            }

            // Execute the query
            List<ProjectDetails> projectsWithDetails = query.ToList();

            Dictionary<string, string> contacts = _contactService.GetAllContacts()
                .ToDictionary(contact => contact.Email, contact => contact.Name);

            List<Project> projects = projectsWithDetails.Select(p => new Project
            {
                Id = p.Project.Id,
                Name = p.Project.Name,
                Description = p.Project.Description,
                AccessType = p.AccessType,
                ProductLine = p.Project.ProductLine,
                Type = p.Project.Type,
                IsProjectLocked = p.LockInfo != null,
                LockingIdContact = p.LockInfo?.IdContact,
                LastModifiedDate = p.Project.LastModifiedDate,
                Admin = p.OwnerEmail != null && contacts.TryGetValue(p.OwnerEmail, out string? ownerName) ? ownerName : string.Empty
            }).ToList();

            // Apply self-project filtering if needed (this needs to be done after mapping)
            if (paginationFilter.IsSelfProject == true)
            {
                string adminEmail = email;
                projects = projects.Where(p =>
                    _context.Permissions.Any(perm =>
                        perm.IdProject == p.Id &&
                        perm.Email == adminEmail &&
                        perm.AccessType == AccessType.Admin)
                ).ToList();
            }

            paginationFilter.CurrentPage = currentPage;
            return GetPaginatedProjects(projects, paginationFilter);
        }
        /// <summary>
        /// Returns a paginated subset of the provided list of projects based on the specified pagination filter.
        /// </summary>
        /// <param name="projects">The full list of <see cref="Project"/> items to paginate.</param>
        /// <param name="paginationFilter">The pagination parameters including page number and page size.</param>
        /// <returns>A paginated list of <see cref="Project"/> items.</returns>
        private IList<Project> GetPaginatedProjects(IList<Project> projects, PaginationFilter paginationFilter)
        {
            paginationFilter.TotalRecords = projects.Count();
            paginationFilter.TotalPages = (int)Math.Ceiling(paginationFilter.TotalRecords / (double)paginationFilter.PageSize);
            int pagesToSkip = (paginationFilter.CurrentPage - 1) * paginationFilter.PageSize;
            return projects.Skip(pagesToSkip).Take(paginationFilter.PageSize).ToList();
        }
        /// <summary>
        /// Checks if a project is currently locked.
        /// </summary>
        /// <param name="projectId">The ID of the project to check.</param>
        /// <returns>True if the project is locked; otherwise, false.</returns>
        private bool IsProjectLocked(int projectId)
        {
            return _context.ProjectLocks.Any(l => l.IdProject == projectId);
        }
        public Project GetProject(int idProject)
        {
            Project? project = _context.Projects.Find(idProject);
            return project ?? throw new NotFoundException(NotFoundTypes.Identifier, "Project");
        }

        public Project GetProjectWithDiagramsAndClasses(int idProject, string email)
        {
            // Validate user has at least view permission for this project
            _permissionService.ValidateUserPermissionOrThrow(email, idProject, AccessType.View);

            Project project = _context.Projects
              .Include(p => p.Diagrams.Where(dia => !dia.IdFolder.HasValue).OrderBy(dia => dia.Name))
              .Include(p => p.TemplateClasses.Where(tc => !tc.IdFolder.HasValue))
                  .ThenInclude(tc => tc.Links.Where(link => !link.IsDeleted))
                      .ThenInclude(link => link.LinkPorts)
                .Include(p => p.TemplateClasses.Where(tc => !tc.IdFolder.HasValue))
                  .ThenInclude(tc => tc.Links.Where(link => !link.IsDeleted))
                      .ThenInclude(link => link.LinkToLinks.Where(lnl => !lnl.IsDeleted))
              .Include(p => p.TemplateEnumerations.Where(te => !te.IdFolder.HasValue))
                  .ThenInclude(te => te.EnumerationLiterals)
              .Include(p => p.TemplateClasses.Where(tc => !tc.IdFolder.HasValue))
                  .ThenInclude(tc => tc.Attributes)
              .Include(p => p.Folders.Where(fol => !fol.ParentFolderId.HasValue))
              .AsNoTracking()
              .AsSplitQuery()
              .FirstOrDefault(p => p.Id == idProject) ?? throw new NotFoundException(NotFoundTypes.Identifier, "Project");

            Permission? projectPermission = _context.Permissions.Where(permission => permission.IdProject == idProject && permission.Email == email).FirstOrDefault();

            List<Diagram> diagramList = _context.Diagrams.Where(dia => dia.IdProject == idProject && dia.IdFolder.HasValue).ToList();
            List<TemplateClass> templateClassList = _context.TemplateClasses
                .Include(tc => tc.Attributes)
                .Include(tc => tc.Links.Where(link => !link.IsDeleted))
                    .ThenInclude(link => link.LinkPorts)
                .Include(tc => tc.Links.Where(link => !link.IsDeleted))
                    .ThenInclude(link => link.LinkToLinks.Where(lnl => !lnl.IsDeleted))
                .Where(_ => _.IdProject == idProject && _.IdFolder.HasValue)
                .ToList();
            List<TemplateEnumeration> templateEnumList = _context.TemplateEnumerations.Include(enu => enu.EnumerationLiterals).Where(_ => _.IdProject == idProject && _.IdFolder.HasValue).ToList();
            List<Folder> folderList = _context.Folders.Where(_ => _.IdProject == idProject && _.ParentFolderId.HasValue).ToList();
            if (projectPermission != null) project.AccessType = projectPermission.AccessType;

            if (project != null && project.Folders != null)
                PopulateChildFolder(project.Folders, diagramList, templateClassList, templateEnumList, folderList);
            return project;
        }
        private void PopulateChildFolder(ICollection<Folder> folders, List<Diagram> diagramList, List<TemplateClass> templateClassList, List<TemplateEnumeration> templateEnumList, List<Folder> folderList)
        {
            foreach (Folder folder in folders)
            {
                folder.ChildFolders = folderList.Where(_ => _.ParentFolderId == folder.Id).OrderBy(fold => fold.Name).ToList();
                folder.TemplateEnumerations = templateEnumList.Where(_ => _.IdFolder == folder.Id).OrderBy(tempEnu => tempEnu.Name).ToList();
                folder.TemplateClasses = templateClassList.Where(_ => _.IdFolder == folder.Id).OrderBy(tempCls => tempCls.Name).ToList();
                folder.Diagrams = diagramList.Where(_ => _.IdFolder == folder.Id).OrderBy(dia => dia.Name).ToList();
                PopulateChildFolder(folder.ChildFolders, diagramList, templateClassList, templateEnumList, folderList);
            }
        }

        public Project CreateProject(Project project, string email)
        {
            Contact contact = _contactService.GetContact(email) ?? throw new NotFoundException(NotFoundTypes.Identifier, "Contact");
            using IDbContextTransaction transaction = _context.BeginTransaction();
            try
            {
                if (!project.Name.IsNullOrEmpty())
                {
                    project.Diagrams = new List<Diagram>() { new Diagram() { Name = Constants.DefaultDiagramName } };
                    _context.Projects.Add(project);
                    _context.SaveChanges();
                    _permissionService.CreatePermission(new Permission() { Email = email, IdProject = project.Id, AccessType = AccessType.Admin });
                    transaction.Commit();
                    return project;
                }
                else throw new BadRequestException("Project name can not be null", BadRequestException.NameCannotbeNull);
            }
            catch (BadRequestException ex)
            {
                transaction.Rollback();
                throw ex;
            }
        }
        public Project UpdateProject(Project project, string email)
        {
            // Validate user has edit permission for this project
            _permissionService.ValidateUserPermissionOrThrow(email, project.Id, AccessType.Edit);

            if (!IsProjectLocked(project.Id))
            {
                if (!project.Name.IsNullOrEmpty())
                {
                    Project? existingProject = GetProject(project.Id);
                    _context.Entry(existingProject).CurrentValues.SetValues(project);
                    _context.SaveChanges();
                    return existingProject;
                }
                else throw new BadRequestException("Project name can not be null", BadRequestException.NameCannotbeNull);
            }
            else throw new BadRequestException("Access to this project is restricted due to lock status", BadRequestException.ProjectIsLocked);
        }
        public void DeleteProject(int idProject, string email)
        {
            if (!IsProjectLocked(idProject))
            {
                Permission permission = _permissionService.GetProjectPermissionByUser(idProject, email);
                if (permission.AccessType != AccessType.Admin) throw new SecurityException("User have no permission to delete");
                List<int> diagramIDs = _context.Diagrams.Where(dg => dg.IdProject == idProject).Select(diag => diag.Id).ToList();
                IList<LinkPort> linkPortToDel = _context.LinkPorts.Where(lp => diagramIDs.Contains((int)lp.IdDiagram)).ToList();
                IList<Comment> comments = _context.Comments.Where(dia => diagramIDs.Contains(dia.IdDiagram)).ToList();

                if (linkPortToDel.Any())
                    _context.LinkPorts.RemoveRange(linkPortToDel);
                if (comments.Any())
                    _context.Comments.RemoveRange(comments);

                _context.Projects.Remove(permission.Project);
                _context.SaveChanges();
            }
            else throw new BadRequestException("Access to this project is restricted due to lock status", BadRequestException.ProjectIsLocked);
        }
    }
}
