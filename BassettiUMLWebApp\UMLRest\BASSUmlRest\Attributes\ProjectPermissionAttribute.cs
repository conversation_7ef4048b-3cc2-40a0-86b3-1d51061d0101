using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security;

namespace BASSUmlRest.Attributes
{
    /// <summary>
    /// Authorization attribute that validates user permissions for project-based operations
    /// Supports multiple project ID extraction strategies and caching for performance
    /// </summary>
    public class ProjectPermissionAttribute : ActionFilterAttribute
    {
        private readonly AccessType _requiredAccessType;
        private readonly string[] _projectIdParameterNames;
        private readonly bool _allowDiagramIdLookup;

        /// <summary>
        /// Initializes a new instance of the ProjectPermissionAttribute
        /// </summary>
        /// <param name="requiredAccessType">The minimum access type required</param>
        /// <param name="projectIdParameterName">Primary parameter name containing the project ID</param>
        /// <param name="allowDiagramIdLookup">Allow looking up project ID from diagram ID</param>
        public ProjectPermissionAttribute(AccessType requiredAccessType, string projectIdParameterName = "idProject", bool allowDiagramIdLookup = false)
        {
            _requiredAccessType = requiredAccessType;
            _projectIdParameterNames = new[] { projectIdParameterName, "id", "projectId" };
            _allowDiagramIdLookup = allowDiagramIdLookup;
        }

        /// <summary>
        /// Constructor for multiple possible parameter names
        /// </summary>
        public ProjectPermissionAttribute(AccessType requiredAccessType, params string[] projectIdParameterNames)
        {
            _requiredAccessType = requiredAccessType;
            _projectIdParameterNames = projectIdParameterNames;
            _allowDiagramIdLookup = false;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var permissionService = context.HttpContext.RequestServices.GetService<IPermissionService>();
            if (permissionService == null)
            {
                context.Result = new StatusCodeResult(500);
                return;
            }

            // Get user email from claims
            var userEmail = context.HttpContext.User.GetEmployeeEmail() ?? context.HttpContext.User.GetEmail();
            if (string.IsNullOrEmpty(userEmail))
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Get project ID from route parameters or request body
            int projectId = GetProjectIdFromContext(context);
            if (projectId == 0)
            {
                context.Result = new BadRequestObjectResult("Project ID not found in request");
                return;
            }

            // Check cache first for performance
            string cacheKey = $"permission_{userEmail}_{projectId}_{_requiredAccessType}";
            if (context.HttpContext.Items.ContainsKey(cacheKey))
            {
                var cachedResult = (bool)context.HttpContext.Items[cacheKey];
                if (!cachedResult)
                {
                    context.Result = new ObjectResult(new { error = "Access denied" }) { StatusCode = 403 };
                    return;
                }
            }
            else
            {
                try
                {
                    // Validate user permission
                    permissionService.ValidateUserPermissionOrThrow(userEmail, projectId, _requiredAccessType);
                    context.HttpContext.Items[cacheKey] = true;
                }
                catch (SecurityException ex)
                {
                    context.HttpContext.Items[cacheKey] = false;
                    context.Result = new ObjectResult(new { error = ex.Message })
                    {
                        StatusCode = 403
                    };
                    return;
                }
                catch (Exception ex)
                {
                    context.Result = new ObjectResult(new { error = "Permission validation failed" })
                    {
                        StatusCode = 500
                    };
                    return;
                }
            }

            // Store validated project ID and user email for service methods
            context.HttpContext.Items["ValidatedProjectId"] = projectId;
            context.HttpContext.Items["ValidatedUserEmail"] = userEmail;
            context.HttpContext.Items["ValidatedAccessType"] = _requiredAccessType;

            base.OnActionExecuting(context);
        }

        private int GetProjectIdFromContext(ActionExecutingContext context)
        {
            // Try multiple parameter names
            foreach (var paramName in _projectIdParameterNames)
            {
                // First, try route parameters
                if (context.RouteData.Values.ContainsKey(paramName))
                {
                    if (int.TryParse(context.RouteData.Values[paramName]?.ToString(), out int routeProjectId))
                    {
                        return routeProjectId;
                    }
                }

                // Then, try action parameters
                if (context.ActionArguments.ContainsKey(paramName))
                {
                    if (int.TryParse(context.ActionArguments[paramName]?.ToString(), out int paramProjectId))
                    {
                        return paramProjectId;
                    }
                }
            }

            // For request body objects, try to get IdProject property
            foreach (var argument in context.ActionArguments.Values)
            {
                if (argument != null)
                {
                    var projectIdProperty = argument.GetType().GetProperty("IdProject");
                    if (projectIdProperty != null)
                    {
                        var value = projectIdProperty.GetValue(argument);
                        if (value != null && int.TryParse(value.ToString(), out int bodyProjectId))
                        {
                            return bodyProjectId;
                        }
                    }
                }
            }

            // If allowed, try to lookup project ID from diagram ID
            if (_allowDiagramIdLookup)
            {
                var diagramId = GetDiagramIdFromContext(context);
                if (diagramId > 0)
                {
                    var diagramService = context.HttpContext.RequestServices.GetService<IDiagramService>();
                    if (diagramService != null)
                    {
                        try
                        {
                            var diagram = diagramService.GetDiagram(diagramId);
                            return diagram.IdProject;
                        }
                        catch
                        {
                            // Ignore errors in diagram lookup
                        }
                    }
                }
            }

            return 0;
        }

        private int GetDiagramIdFromContext(ActionExecutingContext context)
        {
            var diagramParams = new[] { "idDiagram", "diagramId", "id" };

            foreach (var paramName in diagramParams)
            {
                if (context.RouteData.Values.ContainsKey(paramName))
                {
                    if (int.TryParse(context.RouteData.Values[paramName]?.ToString(), out int routeDiagramId))
                    {
                        return routeDiagramId;
                    }
                }

                if (context.ActionArguments.ContainsKey(paramName))
                {
                    if (int.TryParse(context.ActionArguments[paramName]?.ToString(), out int paramDiagramId))
                    {
                        return paramDiagramId;
                    }
                }
            }

            return 0;
        }
    }
}
