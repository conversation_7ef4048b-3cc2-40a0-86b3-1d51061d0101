using BASSUmlBusiness.Extentions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security;

namespace BASSUmlRest.Attributes
{
    /// <summary>
    /// Authorization attribute that validates user permissions for project-based operations
    /// </summary>
    public class ProjectPermissionAttribute : ActionFilterAttribute
    {
        private readonly AccessType _requiredAccessType;
        private readonly string _projectIdParameterName;

        /// <summary>
        /// Initializes a new instance of the ProjectPermissionAttribute
        /// </summary>
        /// <param name="requiredAccessType">The minimum access type required</param>
        /// <param name="projectIdParameterName">The name of the parameter containing the project ID (default: "idProject")</param>
        public ProjectPermissionAttribute(AccessType requiredAccessType, string projectIdParameterName = "idProject")
        {
            _requiredAccessType = requiredAccessType;
            _projectIdParameterName = projectIdParameterName;
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var permissionService = context.HttpContext.RequestServices.GetService<IPermissionService>();
            if (permissionService == null)
            {
                context.Result = new StatusCodeResult(500);
                return;
            }

            // Get user email from claims
            var userEmail = context.HttpContext.User.GetEmployeeEmail() ?? context.HttpContext.User.GetEmail();
            if (string.IsNullOrEmpty(userEmail))
            {
                context.Result = new UnauthorizedResult();
                return;
            }

            // Get project ID from route parameters or request body
            int projectId = GetProjectIdFromContext(context);
            if (projectId == 0)
            {
                context.Result = new BadRequestObjectResult("Project ID not found in request");
                return;
            }

            try
            {
                // Validate user permission
                permissionService.ValidateUserPermissionOrThrow(userEmail, projectId, _requiredAccessType);
            }
            catch (SecurityException ex)
            {
                context.Result = new ObjectResult(new { error = ex.Message })
                {
                    StatusCode = 403
                };
                return;
            }
            catch (Exception ex)
            {
                context.Result = new ObjectResult(new { error = "Permission validation failed" })
                {
                    StatusCode = 500
                };
                return;
            }

            base.OnActionExecuting(context);
        }

        private int GetProjectIdFromContext(ActionExecutingContext context)
        {
            // First, try to get from route parameters
            if (context.RouteData.Values.ContainsKey(_projectIdParameterName))
            {
                if (int.TryParse(context.RouteData.Values[_projectIdParameterName]?.ToString(), out int routeProjectId))
                {
                    return routeProjectId;
                }
            }

            // Then, try to get from action parameters
            if (context.ActionArguments.ContainsKey(_projectIdParameterName))
            {
                if (int.TryParse(context.ActionArguments[_projectIdParameterName]?.ToString(), out int paramProjectId))
                {
                    return paramProjectId;
                }
            }

            // For request body objects, try to get IdProject property
            foreach (var argument in context.ActionArguments.Values)
            {
                if (argument != null)
                {
                    var projectIdProperty = argument.GetType().GetProperty("IdProject");
                    if (projectIdProperty != null)
                    {
                        var value = projectIdProperty.GetValue(argument);
                        if (value != null && int.TryParse(value.ToString(), out int bodyProjectId))
                        {
                            return bodyProjectId;
                        }
                    }
                }
            }

            return 0;
        }
    }
}
