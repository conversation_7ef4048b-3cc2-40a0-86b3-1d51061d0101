﻿using BASSUmlBusiness.Models;
using BASSUmlBusiness.Pagination;

namespace BASSUmlBusiness.Services.Abstractions
{
    public interface IProjectService
    {
        /// <summary>
        /// Retrieves all projects for a authorised user.
        /// </summary>
        /// <param name="email">User email</param>
        /// <returns>A list of projects belonging to the authorised user.</returns>
        public IList<Project> GetAllProjectsByUser(string email, PaginationFilter paginationFilter, int currentPage = 1);
        /// <summary>
        /// Retrieves a project with the specified ID from the database.
        /// </summary>
        /// <param name="idProject">The ID of the project to retrieve.</param>
        /// <returns>The project object retrieved from the database.</returns>
        public Project GetProject(int idProject);
        /// <summary>
        /// Retrieves a project with its associated diagrams and template classes.
        /// </summary>
        /// <param name="idProject">The ID of the project to retrieve.</param>
        /// <param name="email">User email</param>
        /// <returns>The project with its associated diagrams and template classes.</returns>
        public Project GetProjectWithDiagramsAndClasses(int idProject, string email);
        /// <summary>
        /// Creates a new project.
        /// </summary>
        /// <param name="project">The project to be created.</param>
        /// <param name="email">User email</param>
        /// <returns>The created project.</returns>
        /// <remarks>
        /// This method is responsible for creating a new project using the provided project details.
        /// </remarks>
        Project CreateProject(Project project, string email);
        /// <summary>
        /// Updates an existing project.
        /// </summary>
        /// <param name="project">The project with updated details.</param>
        /// <param name="email">User email for permission validation</param>
        /// <returns>The updated project.</returns>
        Project UpdateProject(Project project, string email);
        /// <summary>
        /// Deletes a project based on its identifier.
        /// </summary>
        /// <param name="idProject">The identifier of the project to be deleted.</param>
        /// <param name="email">User email</param>
        void DeleteProject(int idProject, string email);
    }
}
