{"ast": null, "code": "import _asyncToGenerator from \"D:/GitHub/Bassetti/devint-BASSETTI-GROUP-APP/BassettiUMLWebApp/UMLApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { DefaultDestinationPort, DefaultSourcePort } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../cardinality/cardinality.service\";\nimport * as i2 from \"../../access/access.service\";\nimport * as i3 from \"@angular/material/dialog\";\nimport * as i4 from \"src/app/shared/utils/diagram-utils\";\nimport * as i5 from \"../../property/property.service\";\nimport * as i6 from \"../gojsCommon/gojs-common.service\";\nimport * as i7 from \"../../snackbar/snack-bar.service\";\nexport let GojsCardinalityService = /*#__PURE__*/(() => {\n  class GojsCardinalityService {\n    constructor(cardinalityService, accessService, dialog, diagramUtils, propertyService, gojsCommonService, snackbarService) {\n      this.cardinalityService = cardinalityService;\n      this.accessService = accessService;\n      this.dialog = dialog;\n      this.diagramUtils = diagramUtils;\n      this.propertyService = propertyService;\n      this.gojsCommonService = gojsCommonService;\n      this.snackbarService = snackbarService;\n      this.hasEditAccess = false;\n      this.accessService.accessTypeChanges().subscribe(response => {\n        this.hasEditAccess = response != AccessType.Viewer;\n      });\n      this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n        if (diagram) this.currentDiagram = diagram;\n      });\n    }\n    /**\n     * Validates whether a link between two nodes in a GoJS diagram is allowed based on predefined link relations and existing links.\n     * @param fromNode - The starting node of the link.\n     * @param toNode - The target node of the link.\n     * @param linkRelations - An array defining allowed relations between node categories.\n     * @param linkData - The data object representing the link being validated.\n     * @param gojsDiagram - The GoJS diagram instance containing the nodes and links.\n     * @returns A boolean indicating whether the link is valid.\n     */\n    validateLink(fromNode, toNode, linkRelations, linkData, gojsDiagram) {\n      if (!fromNode?.data || !toNode?.data) return false;\n      const linkDataArray = JSON.parse(gojsDiagram.model.toJson())['linkDataArray'];\n      // To Prevent link between classes from same template class\n      if (fromNode.data.idTemplateClass == toNode.data.idTemplateClass && fromNode.data.id != toNode.data.id) return false;\n      //Condition for changing the toPort of associative class and preventing to draw multiple link for same associative class\n      if (fromNode.data.category == GojsNodeCategory.AssociativeClass && linkDataArray.some(link => link['idAssociativeClass'] && link['idAssociativeClass'] === fromNode.data.idTemplateClass) && linkData == null) {\n        return false;\n      }\n      // Find the relation that matches the category of the fromNode\n      const relation = linkRelations?.find(link => link.from === fromNode.data.category);\n      // If no relation exists for the fromNode category, the link is invalid\n      if (!relation) return false;\n      // Check if the toNode category is allowed by the relation\n      if (!relation.to.includes(toNode.data.category)) return false;\n      // If the linkData is provided, check for specific cardinality rules\n      if (linkData && Object.keys(linkData).length > 0) {\n        if (linkData.category === GojsNodeCategory.LinkToLink) {\n          return this.validateFromNodeLinkToLink(fromNode);\n        } else {\n          return true;\n          // const cardinalityLinks = this.cardinalityService.getLinks();\n          // const isCardinalityValid = cardinalityLinks.some(\n          //   (link) =>\n          //     link.idSourceTempClass === fromNode.data.idTemplateClass ||\n          //     link.idDestinationTempClass === toNode.data.idTemplateClass\n          // );\n          // const isLinkAlreadyExists = linkDataArray.some(\n          //   (link: GojsLinkNode) =>\n          //     link.idFromClass === fromNode.data.id &&\n          //     link.idToClass === toNode.data.id\n          // );\n          // return isCardinalityValid && isLinkAlreadyExists;\n        }\n      } else if (fromNode.data.category == GojsNodeCategory.AssociativeClass) {\n        return this.validateLinkToLink(toNode);\n      }\n      // If all checks pass, the link is valid\n      return true;\n    }\n    validateLinkToLink(toNode) {\n      const linkToLinks = this.cardinalityService.getLinkToLinks();\n      const isLinkAlreadyExists = linkToLinks.some(link => link.idLink === toNode.data.idLink);\n      if (isLinkAlreadyExists) {\n        this.snackbarService.info('snackBar.linkToLinkAlreadyExists');\n      }\n      return !isLinkAlreadyExists;\n    }\n    // For checking the link to link from node port change is valid or not\n    validateFromNodeLinkToLink(fromNode) {\n      const linkToLinks = this.cardinalityService.getLinkToLinks();\n      const isLinkAlreadyExists = linkToLinks.some(link => link.idAssociativeClass === fromNode.data.idTemplateClass);\n      return isLinkAlreadyExists;\n    }\n    /**\n     * Validates the relationship between two nodes (fromNode and toNode) based on the link's relation data.\n     * @param {go.Node} fromNode - The source node of the link to be validated.\n     * @param {go.Node} toNode - The target node of the link to be validated.\n     * @param {go.Link} link - The link to be validated.\n     * @param {go.Diagram} gojsDiagram - The diagram to which the link and nodes belong.\n     * @return {*}  {boolean} - Returns `true` if the link is valid based on the relations, otherwise returns `false`.\n     * @memberof GojsCardinalityService\n     */\n    validateGroupLink(fromNode, toNode, link, gojsDiagram) {\n      if (link && link.data && link.data.linkRelations) {\n        return this.validateLink(fromNode, toNode, link.data.linkRelations, link, gojsDiagram);\n      } else {\n        const linkRelations = [{\n          from: GojsNodeCategory.Class,\n          to: [GojsNodeCategory.Class]\n        }, {\n          from: GojsNodeCategory.AssociativeClass,\n          to: [GojsNodeCategory.LinkLabel]\n        }];\n        return this.validateLink(fromNode, toNode, linkRelations, link, gojsDiagram);\n      }\n    }\n    /**\n     * Maps link data to the format required by the diagram.\n     * @param {CardinalityDetails} link The link data.\n     * @param {go.ObjectData} srcLink The source node data.\n     * @param {go.ObjectData} destLink The destination node data.\n     * @returns {GojsLinkNode} The mapped link data.\n     * @memberOf CardinalityService\n     */\n    mapLinkData(link, srcLink, destLink) {\n      let linkData = {\n        from: srcLink['key'],\n        to: destLink['key'],\n        name: link.name,\n        cardinalityFrom: this.cardinalityService.getLinkTypes().get(link.idLinkType)?.from,\n        cardinalityTo: this.cardinalityService.getLinkTypes().get(link.idLinkType)?.to,\n        key: link.id,\n        id: link.id,\n        idLinkType: link.idLinkType,\n        idSourceTempClass: link.idSourceTempClass,\n        idDestinationTempClass: link.idDestinationTempClass,\n        idFromClass: srcLink['id'],\n        idToClass: destLink['id'],\n        category: GojsNodeCategory.Association,\n        editable: this.hasEditAccess,\n        fromPort: link.linkPorts[0]?.sourcePort,\n        toPort: link.linkPorts[0]?.destinationPort,\n        color: link.color,\n        labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\n        fromComment: link.fromComment,\n        toComment: link.toComment,\n        segmentOffset: link.linkPorts[0]?.segmentOffset\n      };\n      this.cardinalityService.createLinkPort({\n        idLink: link.id,\n        idDiagram: this.currentDiagram.id,\n        sourcePort: link.linkPorts[0]?.sourcePort ?? 'R2',\n        destinationPort: link.linkPorts[0]?.destinationPort ?? 'L2',\n        segmentOffset: '0 0'\n      }).subscribe(createdLinkPort => {\n        this.cardinalityService.addNewLinkPort(link.id, createdLinkPort);\n      });\n      return linkData;\n    }\n    /**\n     * Deletes a link based on the node's data.\n     * @param {go.Node} node - The node containing the link to be deleted.\n     * @param {number} nodeCount - The number of links associated with the node.\n     * @param {go.Diagram} diagram - The diagram containing the node and link to be deleted.\n     * @memberof GojsCardinalityService\n     */\n    deleteLink(node, nodeCount, diagram) {\n      const linkData = node.data;\n      if (nodeCount > 1) {\n        this.cardinalityService.removeLink(+linkData.key);\n        this.cardinalityService.delete(+linkData.key);\n      } else {\n        const dialogRef = this.dialog.open(DialogConfirmationComponent, {\n          width: '320px',\n          data: {\n            title: 'dialog.deleteTitle',\n            reject: 'dialog.no',\n            confirm: 'dialog.yes'\n          }\n        });\n        dialogRef.afterClosed().subscribe(isConfirm => {\n          if (isConfirm) {\n            this.removeLinkFromAllDiagram(linkData);\n          } else {\n            this.removeLinkFromCurrentDiagram(linkData, diagram, false);\n          }\n        });\n      }\n    }\n    /**\n     * Handles linking operations for the diagram.\n     * @private\n     * @param {go.ChangedEvent} event - The change event.\n     * @param {string} action - The action type ('undo' or 'redo').\n     * @memberof DiagramEditorComponent\n     */\n    handleUndoRedoLinking(event, action, isDelete) {\n      if (event.object) {\n        event.object['changes']['iterator'].each(obj => {\n          if (obj['propertyName'] == 'linkDataArray') {\n            const valueParam = isDelete ? 'oldValue' : 'newValue';\n            if (action === 'undo') {\n              if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\n                this.cardinalityService.removeLinkToLink(obj[valueParam].key);\n                this.cardinalityService.deleteLinkToLink(obj[valueParam].key);\n              } else {\n                this.cardinalityService.removeLink(obj[valueParam].key);\n                this.cardinalityService.delete(obj[valueParam].key);\n              }\n            } else {\n              if (obj[valueParam].category == GojsNodeCategory.LinkToLink) {\n                this.cardinalityService.addLinkToLink(obj[valueParam]);\n                this.cardinalityService.undoLinkToLinkDeletion(obj[valueParam].key);\n              } else {\n                this.cardinalityService.addLink(obj[valueParam]);\n                this.cardinalityService.undoLinkDeletion(obj[valueParam].key);\n              }\n            }\n          }\n        });\n      }\n    }\n    /**\n     * Updates the link data after editing the text\n     * @param {GojsLinkNode} linkData - The link data containing the cardinality values and other link properties.\n     * @memberof GojsCardinalityService\n     */\n    updateLinkOnTextEdited(linkData) {\n      let idLinkType;\n      const linkTypes = this.cardinalityService.getLinkTypes();\n      for (let [key, value] of linkTypes.entries()) {\n        if (value.from == linkData.cardinalityFrom && value.to == linkData.cardinalityTo) {\n          idLinkType = key;\n          break;\n        }\n      }\n      if (idLinkType) {\n        this.updateLink({\n          ...linkData,\n          idLinkType: idLinkType,\n          id: linkData.key\n        });\n      }\n    }\n    /**\n     * Updates the link data and modifies the link in the cardinality service.\n     * @private\n     * @param {go.ObjectData} linkData  - The data of the link to be updated, which includes properties like `id`, `name`, and `idLinkType`.\n     * @memberof GojsCardinalityService\n     */\n    updateLink(linkData) {\n      this.cardinalityService.updateLink({\n        id: linkData['id'],\n        name: linkData['name'],\n        idLinkType: linkData['idLinkType'],\n        color: linkData['color'],\n        fromComment: linkData['fromComment'] ?? null,\n        toComment: linkData['toComment'] ?? null,\n        idDestinationTempClass: linkData['idDestinationTempClass'],\n        idSourceTempClass: linkData['idSourceTempClass'],\n        segmentOffset: linkData['segmentOffset'],\n        linkPort: {\n          idDiagram: this.currentDiagram.id,\n          destinationPort: linkData['toPort'],\n          sourcePort: linkData['fromPort'],\n          idLink: linkData['id'],\n          segmentOffset: linkData['segmentOffset']\n        }\n      }).subscribe(link => {\n        const modifiedLink = this.cardinalityService.getLinkById(link.id);\n        modifiedLink.name = link.name;\n        modifiedLink.idLinkType = link.idLinkType;\n        modifiedLink.fromComment = link.fromComment;\n        modifiedLink.toComment = link.toComment;\n        modifiedLink.linkPorts = modifiedLink.linkPorts.map(port => {\n          if (port.idDiagram === this.currentDiagram.id) {\n            port.sourcePort = link.linkPort.sourcePort;\n            port.destinationPort = link.linkPort.destinationPort;\n            port.segmentOffset = link.linkPort.segmentOffset;\n          }\n          return port;\n        });\n        this.cardinalityService.modifyLink(modifiedLink);\n        this.propertyService.setPropertyData(linkData);\n      });\n    }\n    /**\n     * Handles the case when a deleted link is found.\n     * @param {DeletedLink} deletedLink - The deleted link object.\n     * @param {GojsLinkNode} link - The current link object.\n     * @param {CardinalityDetails} newLink - The new link object.\n     * @param {go.ObjectData} fromNode - The source node.\n     * @param {go.ObjectData} toNode - The destination node.\n     */\n    handleDeletedLink(deletedLink, link, newLink, fromNode, toNode, diagram) {\n      this.cardinalityService.removeLinkHistory(deletedLink.id);\n      const createdCardinality = {\n        ...newLink,\n        id: deletedLink.idLink,\n        linkPort: {\n          sourcePort: newLink.sourcePort,\n          destinationPort: newLink.destinationPort,\n          idDiagram: deletedLink.idDiagram,\n          idLink: deletedLink.idLink,\n          segmentOffset: '0 0'\n        }\n      };\n      this.cardinalityService.createLinkPort(createdCardinality.linkPort).subscribe(linkPortResult => {\n        this.cardinalityService.addNewLinkPort(createdCardinality.id, linkPortResult);\n        this.updateLinkProperties(link, createdCardinality, fromNode, toNode, diagram);\n      });\n      this.diagramUtils.removeDeletedLink(deletedLink.id);\n    }\n    /**\n     * Updates the properties of a link in the diagram, including its name, key, category, link type,\n     * @private\n     * @param {GojsLinkNode} link - The existing link node to be updated.\n     * @param {CreatedCardinality} linkData - The updated cardinality data for the link.\n     * @param {go.ObjectData} fromNode - The source node for the link.\n     * @param {go.ObjectData} toNode - The target node for the link.\n     * @param {go.Diagram} diagram - The GoJS diagram where the link resides.\n     * @memberof GojsCardinalityService\n     */\n    updateLinkProperties(link, linkData, fromNode, toNode, diagram) {\n      link.category = GojsNodeCategory.Association;\n      link.key = linkData.id;\n      link.idLinkType = linkData.idLinkType;\n      link.idSourceTempClass = linkData.idSourceTempClass;\n      link.idDestinationTempClass = linkData.idDestinationTempClass;\n      link.idFromClass = fromNode['id'];\n      link.idToClass = toNode['id'];\n      link.name = linkData.name;\n      link.cardinalityFrom = this.cardinalityService.getLinkTypes().get(1)?.from;\n      link.cardinalityTo = this.cardinalityService.getLinkTypes().get(1)?.to;\n      link.fromPort = linkData.linkPort.sourcePort;\n      link.toPort = linkData.linkPort.destinationPort;\n      link.editable = this.hasEditAccess;\n      link.color = linkData.color;\n      link.labelKeys = [`${linkData.id}_${GojsNodeCategory.LinkLabel}`];\n      link.fromComment = linkData.fromComment;\n      link.toComment = linkData.toComment;\n      link.segmentOffset = linkData.linkPort.segmentOffset;\n      diagram.model.updateTargetBindings(link);\n    }\n    updateLinkToLinkProp(link, linkData, diagram) {\n      link.category = GojsNodeCategory.LinkToLink;\n      link.key = linkData.id;\n      link.idLink = linkData.idLink;\n      link.idAssociativeClass = linkData.idAssociativeClass;\n      link.fromPort = linkData.port;\n      link.editable = this.hasEditAccess;\n      diagram.model.updateTargetBindings(link);\n    }\n    /**\n     * Creates a new link based on the provided cardinality data, adds the link to the system,\n     * @param {GojsLinkNode} link - The existing link node to be updated after creating the new link.\n     * @param {CardinalityCreate} newLink - The data required to create the new link, including cardinality information.\n     * @param {go.ObjectData} fromNode - The source node for the new link.\n     * @param {go.ObjectData} toNode - The target node for the new link.\n     * @param {go.Diagram} diagram - The GoJS diagram where the link should be updated.\n     * @memberof GojsCardinalityService\n     */\n    createNewLinkAndUpdate(link, newLink, fromNode, toNode, diagram) {\n      this.cardinalityService.createNewLink(newLink).subscribe(createdLink => {\n        this.cardinalityService.addLink({\n          ...createdLink,\n          linkPorts: [createdLink.linkPort],\n          idFromClass: link.idFromClass,\n          idToClass: link.idToClass,\n          segmentOffset: link.segmentOffset\n        });\n        this.propertyService.setPropertyData(null);\n        this.updateLinkProperties(link, createdLink, fromNode, toNode, diagram);\n        const linkLabelNode = {\n          key: `${createdLink.id}_${GojsNodeCategory.LinkLabel}`,\n          category: GojsNodeCategory.LinkLabel,\n          idLink: createdLink.id,\n          editable: this.hasEditAccess\n        };\n        diagram.model.addNodeData(linkLabelNode);\n        diagram.updateAllRelationshipsFromData();\n      });\n    }\n    createNewLinkToLinkAndUpdate(link, newLink, diagram) {\n      this.cardinalityService.createLinkToLink(newLink).subscribe(createdLink => {\n        this.cardinalityService.addLinkToLink(createdLink);\n        this.updateLinkToLinkProp(link, createdLink, diagram);\n      });\n    }\n    updatePortForLinkToLink(linkObj) {\n      // update port for link to link\n      this.cardinalityService.updateLinkToLink({\n        id: linkObj.key,\n        port: linkObj.fromPort,\n        idAssociativeClass: linkObj.idAssociativeClass,\n        idLink: +linkObj.to.split('_')[0]\n      });\n    }\n    /**\n     * Handles the redrawing of a link by updating its link port properties in the diagram.\n     * @param {GojsLinkNode} linkData - The link data that contains the new port information.\n     * @memberof GojsCardinalityService\n     */\n    handleReDrawnLink(linkData, diagram) {\n      const linkDetails = this.cardinalityService.getLinkById(+linkData.key);\n      const fromNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.from);\n      const toNode = this.diagramUtils.getObjectDataByKey(diagram, linkData.to);\n      if (linkDetails && fromNode && toNode) {\n        const currentDiagramLinkPort = linkDetails.linkPorts.find(port => port.idDiagram === this.currentDiagram.id);\n        if (currentDiagramLinkPort) this.updateLinkPort({\n          ...linkDetails,\n          idDestinationTempClass: toNode['idTemplateClass'],\n          idSourceTempClass: fromNode['idTemplateClass']\n        }, currentDiagramLinkPort, linkData.fromPort, linkData.toPort);\n      }\n    }\n    /**\n     * Updates the link port properties in the diagram for a given link.\n     * @private\n     * @param {CardinalityDetails} linkDetails  - The details of the link being updated, including the link ports.\n     * @param {LinkPort} currentDiagramLinkPort - The link port that corresponds to the current diagram.\n     * @param {string} fromPort - The source port for the link.\n     * @param {string} toPort - The destination port for the link.\n     * @memberof GojsCardinalityService\n     */\n    updateLinkPort(linkDetails, currentDiagramLinkPort, fromPort, toPort) {\n      const linkToUpdate = {\n        id: linkDetails.id,\n        name: linkDetails.name,\n        idLinkType: linkDetails.idLinkType,\n        color: linkDetails.color,\n        fromComment: linkDetails.fromComment,\n        toComment: linkDetails.toComment,\n        idDestinationTempClass: linkDetails.idDestinationTempClass,\n        idSourceTempClass: linkDetails.idSourceTempClass,\n        segmentOffset: currentDiagramLinkPort?.segmentOffset ?? '0 0',\n        linkPort: {\n          ...currentDiagramLinkPort,\n          sourcePort: fromPort,\n          destinationPort: toPort\n        }\n      };\n      const otherDiagramLinkPorts = linkDetails.linkPorts.filter(port => port.idDiagram !== this.currentDiagram.id);\n      this.cardinalityService.updateLink(linkToUpdate).subscribe(response => {\n        this.cardinalityService.modifyLink({\n          ...linkDetails,\n          linkPorts: [...otherDiagramLinkPorts, response.linkPort]\n        });\n      });\n    }\n    /**\n     * Formats and filters the link data into a structure suitable for GoJS link nodes.\n     * @param {ClassEntityDTO[]} classes - An array of class entities that provide the source and destination class details.\n     * @param {DeletedLink[]} linkHistories - An array of deleted link histories to filter out the deleted links.\n     * @return {GojsLinkNode[]} - An array of formatted GoJS link nodes based on the provided classes and links.\n     * @memberof GojsCardinalityService\n     */\n    formatLinkData(classes, linkHistories, diagramId) {\n      const linkTypes = this.cardinalityService.getLinkTypes();\n      const associationLinkData = [];\n      if (classes.length > 0) {\n        this.cardinalityService.getLinks().forEach(link => {\n          const fromClass = classes.find(cls => (cls.id == link.idFromClass || cls.idTemplateClass == link.idSourceTempClass) && cls.isAssociative == false);\n          const toClass = classes.find(cls => (cls.id == link?.idToClass || cls.idTemplateClass == link.idDestinationTempClass) && cls.isAssociative == false);\n          const linkType = linkTypes.get(link.idLinkType);\n          const isDeletedLink = linkHistories.find(deletedLink => deletedLink.idSourceClass == fromClass?.id && deletedLink.idDestinationClass == toClass?.id && deletedLink.idLink === link.id) || {};\n          if (fromClass && toClass && Object.keys(isDeletedLink).length == 0) {\n            const currentDiagramLinkPort = this.getOrCreateLinkPort(link, diagramId);\n            const linkNodeData = {\n              from: `${fromClass?.key?.toString()}`,\n              to: `${toClass?.key?.toString()}`,\n              name: link.name,\n              cardinalityFrom: linkType?.from,\n              cardinalityTo: linkType?.to,\n              key: link.id,\n              id: link.id,\n              idLinkType: link.idLinkType,\n              idSourceTempClass: link.idSourceTempClass,\n              idDestinationTempClass: link.idDestinationTempClass,\n              idFromClass: fromClass?.id,\n              idToClass: toClass?.id,\n              category: GojsNodeCategory.Association,\n              editable: this.hasEditAccess,\n              fromPort: currentDiagramLinkPort?.sourcePort,\n              toPort: currentDiagramLinkPort?.destinationPort,\n              color: link.color,\n              labelKeys: [`${link.id}_${GojsNodeCategory.LinkLabel}`],\n              fromComment: link.fromComment,\n              toComment: link.toComment,\n              segmentOffset: link.linkPorts.find(port => port.idDiagram == diagramId)?.segmentOffset ?? '0 0' // Offset the label slightly from the midpoint\n            };\n            associationLinkData.push(linkNodeData);\n          }\n        });\n      }\n      return associationLinkData;\n    }\n    generateLinkToLinkNodes(associationLinks, classes) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const processedIds = new Set();\n        const linkToLinks = _this.cardinalityService.getLinkToLinks();\n        const linkToLinkNodes = [];\n        for (const classNode of classes) {\n          if (!classNode.isAssociative) continue;\n          const linkToLink = linkToLinks.find(link => link.idAssociativeClass === classNode.idTemplateClass);\n          if (linkToLink) {\n            const linkNode = associationLinks.find(link => link.id === linkToLink.idLink);\n            if (!linkNode) continue;\n            if (processedIds.has(linkToLink.idLink)) continue;\n            processedIds.add(linkToLink.idLink);\n            linkToLinkNodes.push(_this.formatLinkToLinkData(linkToLink, classNode.key.toString()));\n          }\n        }\n        return linkToLinkNodes;\n      })();\n    }\n    /**\n     * Retrieves an existing link port for the current diagram or creates a new one if it doesn't exist.\n     * @private\n     * @param {CardinalityDetails} link - The link whose port is being retrieved or created.\n     * @return {LinkPort} The existing or newly created link port associated with the current diagram.\n     * @memberof GojsCardinalityService\n     */\n    getOrCreateLinkPort(link, diagramId) {\n      let currentDiagramLinkPort = link.linkPorts?.find(port => port.idDiagram === diagramId);\n      const linkPort = link.linkPorts ? {\n        idDiagram: diagramId,\n        idLink: link.id,\n        sourcePort: link.linkPorts[0]?.sourcePort ? link.linkPorts[0]?.sourcePort : DefaultSourcePort,\n        destinationPort: link.linkPorts[0]?.destinationPort ? link.linkPorts[0]?.destinationPort : DefaultDestinationPort,\n        segmentOffset: link.linkPorts[0]?.segmentOffset ?? '0 0'\n      } : {\n        idDiagram: diagramId,\n        idLink: link.id,\n        sourcePort: link.fromPort ?? DefaultSourcePort,\n        destinationPort: link.toPort ?? DefaultDestinationPort,\n        segmentOffset: '0 0'\n      };\n      if (currentDiagramLinkPort) {\n        return currentDiagramLinkPort;\n      } else {\n        const ports = link.linkPorts ? link.linkPorts : [];\n        this.cardinalityService.createLinkPort(linkPort).subscribe(createdLinkPort => {\n          currentDiagramLinkPort = createdLinkPort;\n          this.cardinalityService.modifyLink({\n            ...link,\n            linkPorts: [...ports, currentDiagramLinkPort]\n          });\n        });\n        return currentDiagramLinkPort ?? linkPort;\n      }\n    }\n    /**\n     * Creates links for a specific class in the GoJS diagram based on its palette ID.\n     * @param {number} idPalette  - The ID of the class in the palette to create links for.\n     * @param {go.Diagram} gojsDiagram - The GoJS diagram where the links should be created.\n     * @memberof GojsCardinalityService\n     */\n    createLinksForPaletteClass(idPalette, gojsDiagram) {\n      if (gojsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] == idPalette).length == 1 && this.cardinalityService.getLinks().find(link => link.idSourceTempClass == idPalette || link.idDestinationTempClass == idPalette)) {\n        const links = this.getRelevantLinks(idPalette);\n        links.forEach(link => {\n          this.createLinkForClass(link, gojsDiagram);\n        });\n      }\n      //For add the link to link node in diagram during drop library\n      if (gojsDiagram.model.nodeDataArray.filter(node => node['idTemplateClass'] == idPalette && node['category'] == GojsNodeCategory.AssociativeClass).length == 1 && this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == idPalette)) {\n        const linkToLink = this.cardinalityService.getLinkToLinks().find(link => link.idAssociativeClass == idPalette);\n        const isPresentLinkToLink = gojsDiagram.model.linkDataArray.some(link => link['idLink'] == linkToLink?.idLink);\n        if (linkToLink && !isPresentLinkToLink) this.formatLinkToLinkObj(linkToLink, gojsDiagram);\n      }\n    }\n    /**\n     * Retrieves the relevant links associated with a specific class from the palette\n     * @param {number} idPalette - The ID of the class in the palette to create links for.\n     * @return   {CardinalityDetails[]} - An array of relevant links associated with the class.\n     * @memberof GojsCardinalityService\n     */\n    getRelevantLinks(idPalette) {\n      return this.cardinalityService.getLinks().filter(linkData => linkData.idDestinationTempClass === idPalette || linkData.idSourceTempClass === idPalette);\n    }\n    /**\n     * Creates a link between two nodes in the GoJS diagram based on the provided link data.\n     * @private\n     * @param {CardinalityDetails} link - The link data containing source and destination class IDs.\n     * @param {go.Diagram} goJsDiagram - The GoJS diagram where the link should be created.\n     * @memberof GojsCardinalityService\n     */\n    createLinkForClass(link, goJsDiagram) {\n      const srcLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idSourceTempClass);\n      const destLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idDestinationTempClass);\n      if (srcLink && destLink) {\n        const linkData = this.mapLinkData(link, srcLink, destLink);\n        goJsDiagram.model.addNodeData({\n          key: `${linkData.id}_${GojsNodeCategory.LinkLabel}`,\n          category: GojsNodeCategory.LinkLabel,\n          idLink: linkData.id,\n          editable: linkData.editable\n        });\n        const linkToLinkNode = this.cardinalityService.getLinkToLinks().find(link => link.idLink == linkData.id);\n        if (linkToLinkNode) this.formatLinkToLinkObj(linkToLinkNode, goJsDiagram);\n        goJsDiagram.updateAllRelationshipsFromData();\n        goJsDiagram.model.addLinkData(linkData);\n      }\n    }\n    formatLinkToLinkObj(link, goJsDiagram) {\n      const srcLink = this.diagramUtils.findNodeByIdTemplateClass(goJsDiagram, link.idAssociativeClass);\n      if (srcLink) {\n        const linkData = {\n          from: srcLink['key'],\n          key: link.id,\n          idLink: link.idLink,\n          to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\n          idAssociativeClass: link.idAssociativeClass,\n          category: GojsNodeCategory.LinkToLink,\n          editable: this.hasEditAccess,\n          fromPort: link.port\n        };\n        goJsDiagram.model.addLinkData(linkData);\n      }\n    }\n    /**\n     * Removes all links related to the specified palette ID.\n     * @param {number} idPalette - The ID of the palette whose related links should be removed.\n     * @memberof GojsCardinalityService\n     */\n    removeRelatedLinks(idPalette) {\n      const relatedLinks = this.cardinalityService.getLinks().filter(link => link.idDestinationTempClass === idPalette || link.idSourceTempClass === idPalette);\n      this.cardinalityService.removeLinks(relatedLinks);\n    }\n    /**\n     * Removes a link from all diagrams associated with the provided link data.\n     * @param {GojsLinkNode} linkData - The link data to be removed.\n     * @memberof GojsCardinalityService\n     */\n    removeLinkFromAllDiagram(linkData) {\n      this.cardinalityService.removeLink(+linkData.key);\n      this.cardinalityService.delete(+linkData.key);\n      this.diagramUtils.removeDeletedLink(+linkData.key);\n    }\n    /**\n     * Removes a link from the current diagram, with an option for temporary or permanent removal.\n     * @param {GojsLinkNode} linkData - The link data to be removed.\n     * @param {go.Diagram} diagram  - The GoJS diagram from which the link is to be removed.\n     * @param {boolean} isPermanent - Flag to indicate if the removal is permanent.\n     * @memberof GojsCardinalityService\n     */\n    removeLinkFromCurrentDiagram(linkData, diagram, isPermanent) {\n      if (!isPermanent) this.cardinalityService.createDeletedLinkHistory({\n        idDiagram: this.currentDiagram.id,\n        idSourceClass: linkData.idFromClass,\n        idDestinationClass: linkData.idToClass,\n        idLink: +linkData.key\n      });\n      const linkDetails = this.cardinalityService.getLinkById(+linkData.key);\n      const currentDiagramLinkPort = linkDetails.linkPorts.find(port => port.idDiagram === this.currentDiagram.id);\n      if (currentDiagramLinkPort) {\n        if (!isPermanent) this.cardinalityService.deleteLinkPort(currentDiagramLinkPort.id);\n        this.cardinalityService.removeExistingLinkPort(linkDetails.id, currentDiagramLinkPort.id);\n      }\n      diagram.model.removeLinkData(linkData);\n    }\n    updateLinkFromProperty(updatedNode, diagram) {\n      let link = null;\n      diagram.model.linkDataArray.forEach(linkData => {\n        if (linkData['key'] === updatedNode.key && linkData['category'] === updatedNode.category) {\n          link = linkData;\n        }\n      });\n      if (!link) return;\n      const linkData = link;\n      this.gojsCommonService.setDataProperties(diagram.model, linkData, {\n        name: updatedNode.name,\n        fromComment: updatedNode.fromComment,\n        toComment: updatedNode.toComment,\n        color: updatedNode.color\n      });\n      const modifiedLink = this.cardinalityService.getLinkById(updatedNode.id);\n      modifiedLink.name = updatedNode.name;\n      modifiedLink.fromComment = updatedNode.fromComment;\n      modifiedLink.toComment = updatedNode.toComment;\n      modifiedLink.color = updatedNode.color;\n      this.cardinalityService.modifyLink(modifiedLink);\n    }\n    formatLinkToLinkData(link, fromClassKey) {\n      const linkToLinkNode = {\n        from: fromClassKey,\n        to: `${link.idLink}_${GojsNodeCategory.LinkLabel}`,\n        key: link.id,\n        idLink: link.idLink,\n        idAssociativeClass: link.idAssociativeClass,\n        category: GojsNodeCategory.LinkToLink,\n        editable: this.hasEditAccess,\n        fromPort: link.port\n      };\n      return linkToLinkNode;\n    }\n    static #_ = this.ɵfac = function GojsCardinalityService_Factory(t) {\n      return new (t || GojsCardinalityService)(i0.ɵɵinject(i1.CardinalityService), i0.ɵɵinject(i2.AccessService), i0.ɵɵinject(i3.MatDialog), i0.ɵɵinject(i4.DiagramUtils), i0.ɵɵinject(i5.PropertyService), i0.ɵɵinject(i6.GojsCommonService), i0.ɵɵinject(i7.SnackBarService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GojsCardinalityService,\n      factory: GojsCardinalityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return GojsCardinalityService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}