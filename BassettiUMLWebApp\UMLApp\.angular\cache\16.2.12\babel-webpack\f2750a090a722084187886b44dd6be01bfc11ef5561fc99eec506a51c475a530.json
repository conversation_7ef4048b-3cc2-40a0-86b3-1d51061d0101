{"ast": null, "code": "import { SelectionModel } from '@angular/cdk/collections';\nimport { NestedTreeControl } from '@angular/cdk/tree';\nimport { computed, effect, signal } from '@angular/core';\nimport { MatTreeNestedDataSource } from '@angular/material/tree';\nimport { TreeNodeContextMenu } from 'src/app/shared/configs/contextMenuConfig';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { ClassWrapperCategory, DiagramWrapperCategory, EnumWrapperCategory } from 'src/app/shared/utils/constants';\nimport { DialogConfirmationComponent } from '../dialog-confirmation/dialog-confirmation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/treeNode/tree-node.service\";\nimport * as i3 from \"src/app/shared/utils/diagram-utils\";\nimport * as i4 from \"../../services/contextMenuAction/context-menu-action.service\";\nimport * as i5 from \"../../services/access/access.service\";\nimport * as i6 from \"../../services/property/property.service\";\nimport * as i7 from \"../../services/navbar/navbar.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/material/tooltip\";\nimport * as i11 from \"@angular/material/icon\";\nimport * as i12 from \"@angular/material/menu\";\nimport * as i13 from \"@angular/material/tree\";\nimport * as i14 from \"@angular/material/list\";\nimport * as i15 from \"@angular/forms\";\nimport * as i16 from \"../search-bar/search-bar.component\";\nimport * as i17 from \"@ngx-translate/core\";\nimport * as i18 from \"../../../shared/pipes/truncate.pipe\";\nfunction LibraryTreeComponent_app_search_bar_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-search-bar\", 9);\n    i0.ɵɵlistener(\"searchChanged\", function LibraryTreeComponent_app_search_bar_0_Template_app_search_bar_searchChanged_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSearch($event));\n    });\n    i0.ɵɵpipe(1, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(1, 1, \"diagram.search\"));\n  }\n}\nfunction LibraryTreeComponent_div_1_mat_list_item_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 12);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_div_1_mat_list_item_5_Template_mat_list_item_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const diagram_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.selectDiagramNode($event, diagram_r11));\n    });\n    i0.ɵɵelement(1, \"span\", 13);\n    i0.ɵɵelementStart(2, \"span\", 14);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"truncate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const diagram_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected-diagram\", diagram_r11.tag === ctx_r10.currentDiagramTag());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", diagram_r11.icon, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r10.shouldShowTooltip(diagram_r11.name, 25) ? diagram_r11.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 5, diagram_r11.name, 25), \" \");\n  }\n}\nfunction LibraryTreeComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"h3\");\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-list\");\n    i0.ɵɵtemplate(5, LibraryTreeComponent_div_1_mat_list_item_5_Template, 5, 8, \"mat-list-item\", 11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 2, \"diagram.diagramsList\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.searchResults());\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = i0.ɵɵnextContext().$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r16.shouldShowTooltip(result_r15.name, 25) ? result_r15.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 2, result_r15.name, 25), \"\");\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(result_r15.name = $event);\n    })(\"blur\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(1);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.saveRename(result_r15, _r20.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const _r20 = i0.ɵɵreference(1);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.saveRename(result_r15, _r20.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const result_r15 = i0.ɵɵnextContext().$implicit;\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.cancelRename(result_r15));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", result_r15.name);\n  }\n}\nfunction LibraryTreeComponent_div_2_mat_list_item_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-list-item\", 17);\n    i0.ɵɵlistener(\"contextmenu\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_contextmenu_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.onRightClick($event, result_r15));\n    })(\"dragstart\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragstart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onDragStart($event, result_r15));\n    })(\"click\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.selectDiagramNode($event, result_r15));\n    })(\"dragover\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dragover_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.onDragOver($event, result_r15));\n    })(\"drop\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.onMove($event, result_r15));\n    })(\"dblclick\", function LibraryTreeComponent_div_2_mat_list_item_2_Template_mat_list_item_dblclick_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r32);\n      const result_r15 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.enableRename(result_r15));\n    });\n    i0.ɵɵelement(1, \"span\", 13);\n    i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_mat_list_item_2_ng_container_2_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(3, LibraryTreeComponent_div_2_mat_list_item_2_ng_template_3_Template, 2, 1, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r15 = ctx.$implicit;\n    const _r17 = i0.ɵɵreference(4);\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ctx_r14.selection.isSelected(result_r15))(\"selected-diagram\", result_r15.tag === ctx_r14.currentDiagramTag());\n    i0.ɵɵproperty(\"draggable\", result_r15.isDraggable && ctx_r14.hasEditAccessOnly() && ctx_r14.hasDiagram());\n    i0.ɵɵattribute(\"data-node-tag\", result_r15.tag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", result_r15.icon, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !result_r15.isRenaming)(\"ngIfElse\", _r17);\n  }\n}\nfunction LibraryTreeComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-list\");\n    i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_mat_list_item_2_Template, 5, 9, \"mat-list-item\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchResults());\n  }\n}\nfunction LibraryTreeComponent_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"p\", 23);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"diagram.notFound\"));\n  }\n}\nfunction LibraryTreeComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryTreeComponent_ng_template_3_div_0_Template, 4, 3, \"div\", 22);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.searchResults().length === 0 && ctx_r4.isSearching() && !ctx_r4.showOnlyDiagrams());\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r39 = i0.ɵɵnextContext().$implicit;\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r40.shouldShowTooltip(node_r39.name, 25) ? node_r39.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 2, node_r39.name, 25));\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(node_r39.name = $event);\n    })(\"blur\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const _r44 = i0.ɵɵreference(1);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.saveRename(node_r39, _r44.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const _r44 = i0.ɵɵreference(1);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.saveRename(node_r39, _r44.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const node_r39 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.cancelRename(node_r39));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r39 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", node_r39.name);\n  }\n}\nfunction LibraryTreeComponent_mat_tree_node_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-tree-node\", 24);\n    i0.ɵɵlistener(\"contextmenu\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_contextmenu_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onRightClick($event, node_r39));\n    })(\"dragstart\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragstart_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onDragStart($event, node_r39));\n    })(\"click\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.selectDiagramNode($event, node_r39));\n    })(\"dragover\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dragover_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.onDragOver($event, node_r39));\n    })(\"drop\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_drop_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.onMove($event, node_r39));\n    })(\"dblclick\", function LibraryTreeComponent_mat_tree_node_7_Template_mat_tree_node_dblclick_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.enableRename(node_r39));\n    });\n    i0.ɵɵelementStart(1, \"span\", 25);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_tree_node_7_Template_span_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r56);\n      const node_r39 = restoredCtx.$implicit;\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.treeControl.toggle(node_r39));\n    });\n    i0.ɵɵelement(2, \"span\", 26);\n    i0.ɵɵtemplate(3, LibraryTreeComponent_mat_tree_node_7_ng_container_3_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(4, LibraryTreeComponent_mat_tree_node_7_ng_template_4_Template, 2, 1, \"ng-template\", null, 19, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r39 = ctx.$implicit;\n    const _r41 = i0.ɵɵreference(5);\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"selected\", ctx_r5.selection.isSelected(node_r39))(\"selected-diagram\", node_r39.tag === ctx_r5.currentDiagramTag());\n    i0.ɵɵproperty(\"draggable\", node_r39.isDraggable && ctx_r5.hasEditAccessOnly() && ctx_r5.hasDiagram());\n    i0.ɵɵattribute(\"data-node-tag\", node_r39.tag);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", node_r39.icon, i0.ɵɵsanitizeHtml)(\"draggable\", node_r39.isDraggable && ctx_r5.hasEditAccessOnly());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !node_r39.isRenaming)(\"ngIfElse\", _r41);\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"truncate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const node_r63 = i0.ɵɵnextContext().$implicit;\n    const ctx_r64 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"matTooltip\", ctx_r64.shouldShowTooltip(node_r63.name, 25) ? node_r63.name : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(3, 2, node_r63.name, 25));\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 20, 21);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(node_r63.name = $event);\n    })(\"blur\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_blur_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const _r68 = i0.ɵɵreference(1);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.saveRename(node_r63, _r68.value));\n    })(\"keydown.enter\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const _r68 = i0.ɵɵreference(1);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.saveRename(node_r63, _r68.value));\n    })(\"keydown.escape\", function LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template_input_keydown_escape_0_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const node_r63 = i0.ɵɵnextContext().$implicit;\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.cancelRename(node_r63));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const node_r63 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", node_r63.name);\n  }\n}\nfunction LibraryTreeComponent_mat_nested_tree_node_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r80 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-nested-tree-node\", 27);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_mat_nested_tree_node_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.selectDiagramNode($event, node_r63));\n    });\n    i0.ɵɵelementStart(1, \"div\", 28);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.treeControl.toggle(node_r63));\n    })(\"contextmenu\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_contextmenu_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.onRightClick($event, node_r63));\n    })(\"dragstart\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragstart_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onDragStart($event, node_r63));\n    })(\"dragenter\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragenter_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.onDragEnter($event, node_r63));\n    })(\"dragleave\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragleave_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.onDragLeave($event, node_r63));\n    })(\"dragover\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dragover_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.onDragOver($event, node_r63));\n    })(\"drop\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_drop_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.onMove($event, node_r63));\n    })(\"dblclick\", function LibraryTreeComponent_mat_nested_tree_node_8_Template_div_dblclick_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r80);\n      const node_r63 = restoredCtx.$implicit;\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.enableRename(node_r63));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\", 29);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 30);\n    i0.ɵɵelement(5, \"span\", 26);\n    i0.ɵɵtemplate(6, LibraryTreeComponent_mat_nested_tree_node_8_ng_container_6_Template, 4, 5, \"ng-container\", 18);\n    i0.ɵɵtemplate(7, LibraryTreeComponent_mat_nested_tree_node_8_ng_template_7_Template, 2, 1, \"ng-template\", null, 31, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 32);\n    i0.ɵɵelementContainer(10, 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const node_r63 = ctx.$implicit;\n    const _r65 = i0.ɵɵreference(8);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-node-tag\", node_r63.tag);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"drop-target\", ctx_r6.isDraggedOver())(\"can-drop\", ctx_r6.isDraggedOver() && ctx_r6.isValidDropTarget(node_r63))(\"cannot-drop\", ctx_r6.isDraggedOver() && !ctx_r6.isValidDropTarget(node_r63))(\"dragging\", ctx_r6.isDragging() && ctx_r6.draggedNode() === node_r63)(\"selected\", ctx_r6.selection.isSelected(node_r63));\n    i0.ɵɵproperty(\"draggable\", node_r63.isDraggable && ctx_r6.hasEditAccessOnly() && ctx_r6.hasDiagram());\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", \"Toggle \" + node_r63.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.treeControl.isExpanded(node_r63) ? \"expand_more\" : \"chevron_right\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", node_r63.icon, i0.ɵɵsanitizeHtml)(\"draggable\", node_r63.isDraggable && ctx_r6.hasEditAccessOnly());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !node_r63.isRenaming)(\"ngIfElse\", _r65);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"library-tree-invisible\", !ctx_r6.treeControl.isExpanded(node_r63));\n  }\n}\nfunction LibraryTreeComponent_ng_container_9_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function LibraryTreeComponent_ng_container_9_button_2_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const option_r90 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r91.onAction(option_r90.actionName));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r90 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r90.label, \" \");\n  }\n}\nfunction LibraryTreeComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 34);\n    i0.ɵɵtemplate(2, LibraryTreeComponent_ng_container_9_button_2_Template, 2, 1, \"button\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r7.getRightClickMenuStyle());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.contextMenuOptions());\n  }\n}\nexport let LibraryTreeComponent = /*#__PURE__*/(() => {\n  class LibraryTreeComponent {\n    // Add host listener for delete key\n    onDeleteKey() {\n      if (this.hasEditAccessOnly() && this.selection.selected.length > 0) {\n        this.deleteSelectedNodes();\n      }\n    }\n    set expandNodeTag(tag) {\n      if (tag) {\n        // Add a delay to ensure the tree is fully rendered\n        setTimeout(() => {\n          this.expandNodeByTag(tag);\n        }, 200);\n      }\n    }\n    constructor(_dialog, treeNodeService, diagramUtils, _contextMenuActionService, _accessService, _propertyService, _navbarService, _ngZone, router) {\n      this._dialog = _dialog;\n      this.treeNodeService = treeNodeService;\n      this.diagramUtils = diagramUtils;\n      this._contextMenuActionService = _contextMenuActionService;\n      this._accessService = _accessService;\n      this._propertyService = _propertyService;\n      this._navbarService = _navbarService;\n      this._ngZone = _ngZone;\n      this.router = router;\n      // Tree control and data source\n      this.treeControl = new NestedTreeControl(node => node.children);\n      this.dataSource = new MatTreeNestedDataSource();\n      // Convert component state to signals\n      this._isDisplayContextMenu = signal(false);\n      this._rightClickMenuPositionX = signal(0);\n      this._rightClickMenuPositionY = signal(0);\n      this._currentDiagram = signal(null);\n      this._currentNode = signal(null);\n      this._contextMenuOptions = signal([]);\n      this._hasEditAccessOnly = signal(false);\n      this._draggedNode = signal(null);\n      this._treeNodeData = signal(null);\n      this._selectedTreeNode = signal(null);\n      this._selectedNodes = signal(new Set());\n      this._isRenaming = signal(false);\n      this._previousName = signal('');\n      this._currentDiagramTag = signal('');\n      this._searchResults = signal([]);\n      this._hasDiagram = signal(true);\n      this._isSearching = signal(false);\n      this._showOnlyDiagrams = signal(false);\n      this._isDraggedOver = signal(false);\n      this._isDragging = signal(false);\n      this._currentDragTarget = signal(null);\n      // Readonly signals for template access\n      this.isDisplayContextMenu = this._isDisplayContextMenu.asReadonly();\n      this.rightClickMenuPositionX = this._rightClickMenuPositionX.asReadonly();\n      this.rightClickMenuPositionY = this._rightClickMenuPositionY.asReadonly();\n      this.currentDiagram = this._currentDiagram.asReadonly();\n      this.contextMenuOptions = this._contextMenuOptions.asReadonly();\n      this.hasEditAccessOnly = this._hasEditAccessOnly.asReadonly();\n      this.draggedNode = this._draggedNode.asReadonly();\n      this.treeNodeData = this._treeNodeData.asReadonly();\n      this.selectedTreeNode = this._selectedTreeNode.asReadonly();\n      this.selectedNodes = this._selectedNodes.asReadonly();\n      this.isRenaming = this._isRenaming.asReadonly();\n      this.previousName = this._previousName.asReadonly();\n      this.currentDiagramTag = this._currentDiagramTag.asReadonly();\n      this.searchResults = this._searchResults.asReadonly();\n      this.hasDiagram = this._hasDiagram.asReadonly();\n      this.isSearching = this._isSearching.asReadonly();\n      this.showOnlyDiagrams = this._showOnlyDiagrams.asReadonly();\n      this.isDraggedOver = this._isDraggedOver.asReadonly();\n      this.isDragging = this._isDragging.asReadonly();\n      this.currentDragTarget = this._currentDragTarget.asReadonly();\n      // Computed signal to check if tree is ready for expansion\n      this.isTreeReady = computed(() => {\n        return this._treeNodeData() !== null && this.dataSource.data.length > 0;\n      });\n      // Keep selection model for now\n      this.selection = new SelectionModel(true, []); // true enables multi-select\n      this.hasChild = (_, node) => {\n        return !!node.children && node.children.length > 0;\n      };\n      // Effect for navbar version history changes\n      effect(() => {\n        const isRightPanelOpened = this._navbarService.showVersionHistory();\n        this._ngZone.run(() => {\n          this.toggleDiagramsView(isRightPanelOpened);\n        });\n      }, {\n        allowSignalWrites: true\n      });\n      // Subscribe to library details changes (using existing Observable method)\n      this.treeNodeService.getLibraryDetails().subscribe(treeNode => {\n        if (treeNode) {\n          this._treeNodeData.set(treeNode);\n          this.dataSource.data = [];\n          this.dataSource.data = [treeNode];\n          const nodes = this.treeControl.getDescendants(treeNode);\n          this.treeNodeService.descendantTreeNodes = nodes;\n          // If we have a current diagram, expand to show it\n          const currentDiagramTag = this._currentDiagramTag();\n          if (currentDiagramTag) {\n            setTimeout(() => {\n              this.expandNodeByTag(currentDiagramTag);\n            }, 100);\n          }\n        }\n      });\n      // Subscribe to active diagram changes (using existing Observable method)\n      this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n        if (diagram) {\n          this._hasDiagram.set(true);\n          this._currentDiagram.set(diagram);\n          this._currentDiagramTag.set(`atTag${GojsNodeCategory.Diagram}_${diagram.id}`);\n          // When a diagram is activated, expand the tree to show it\n          // Add a delay to ensure the tree data is fully loaded\n          setTimeout(() => {\n            this.expandNodeByTag(this._currentDiagramTag());\n          }, 100);\n        } else {\n          this._propertyService.setPropertyData(null);\n          this._hasDiagram.set(false);\n        }\n      });\n      // Subscribe to access type changes (using existing Observable method)\n      this._accessService.accessTypeChanges().subscribe(accessType => {\n        this._hasEditAccessOnly.set(accessType != AccessType.Viewer);\n      });\n    }\n    ngOnInit() {\n      // The subscriptions are now handled in the constructor with effects\n      // This method can be used for any additional initialization if needed\n    }\n    onSearch(searchTerm) {\n      if (searchTerm) {\n        this._isSearching.set(true);\n        this._searchResults.set(this.searchNodes(searchTerm));\n        this._propertyService.setPropertyData(null);\n      } else {\n        this._isSearching.set(false);\n        this._searchResults.set([]);\n      }\n    }\n    toggleDiagramsView(showOnlyDiagrams) {\n      this._showOnlyDiagrams.set(showOnlyDiagrams);\n      // If toggling to diagram-only view, populate the diagrams list\n      if (this.showOnlyDiagrams()) {\n        this.populateDiagramsList();\n      }\n    }\n    populateDiagramsList() {\n      this._searchResults.set(this.getAllNodes().filter(node => node.category === GojsNodeCategory.Diagram));\n    }\n    // Search through nodes recursively\n    searchNodes(searchTerm, nodes = this.dataSource.data) {\n      let results = [];\n      for (const node of nodes) {\n        // Check if current node matches search\n        if (this.nodeFilterCriteria(node) && node.name.toLowerCase().includes(searchTerm.toLowerCase())) {\n          results.push(node);\n        }\n        // If node has children, search them too\n        if (node.children && node.children.length > 0) {\n          results = results.concat(this.searchNodes(searchTerm, node.children));\n        }\n      }\n      return results;\n    }\n    nodeFilterCriteria(node) {\n      return node.category !== ClassWrapperCategory && node.category !== EnumWrapperCategory && node.category !== DiagramWrapperCategory && node.category !== GojsNodeCategory.Project;\n    }\n    // Clear search and reset view\n    clearSearch() {\n      this._searchResults.set([]);\n      this._propertyService.setPropertyData(null);\n    }\n    // Handle selection of search result\n    selectSearchResult(event, node) {\n      this.selection.select(node);\n      this.selectDiagramNode(event, node);\n    }\n    // Method to delete selected nodes\n    deleteSelectedNodes() {\n      const selectedNodes = this.selection.selected;\n      if (selectedNodes.length === 0) return;\n      const dialogRef = this._dialog.open(DialogConfirmationComponent, {\n        width: '320px',\n        data: {\n          title: 'dialog.title',\n          reject: 'dialog.no',\n          confirm: 'dialog.yes'\n        }\n      });\n      dialogRef.afterClosed().subscribe(isConfirm => {\n        if (isConfirm) {\n          // Delete each selected node\n          this._contextMenuActionService.deleteSelectedNodes(selectedNodes);\n          // Clear selection after deletion\n          this.selection.clear();\n        }\n      });\n    }\n    canDiagramDraggable(node) {\n      if (node && node.category === GojsNodeCategory.Diagram) {\n        const treeNodeData = this.treeNodeData();\n        if (treeNodeData) {\n          const parentFolderNode = this.treeNodeService.findParentNode(node.parentTag, treeNodeData);\n          if (parentFolderNode && parentFolderNode.category === GojsNodeCategory.Folder) return true;else return true;\n        }\n      }\n      return true;\n    }\n    onRightClick(event, node) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._currentNode.set(node);\n      const contextMenu = TreeNodeContextMenu.find(menu => menu.category === node.category && menu.isDisplay);\n      // If the node is not already selected and we have multiple nodes selected,\n      // clear the selection and select only this node\n      if (this.selection.selected.length > 1 && !this.selection.isSelected(node)) {\n        this.selection.clear();\n        this.selection.select(node);\n      }\n      if (contextMenu) {\n        this._contextMenuOptions.set(this.selection.selected.length <= 1 ? contextMenu.options : contextMenu.options.filter(option => option.label == 'Delete'));\n        this._isDisplayContextMenu.set(true);\n        this._rightClickMenuPositionX.set(event.clientX);\n        this._rightClickMenuPositionY.set(event.clientY);\n      }\n    }\n    documentClick() {\n      this._isDisplayContextMenu.set(false);\n      this.selection.clear();\n    }\n    getRightClickMenuStyle() {\n      return {\n        position: 'fixed',\n        left: `${this.rightClickMenuPositionX()}px`,\n        top: `${this.rightClickMenuPositionY()}px`\n      };\n    }\n    selectDiagramNode(event, treeNode) {\n      event.stopPropagation();\n      this._isDisplayContextMenu.set(false);\n      if (treeNode.category === ClassWrapperCategory || treeNode.category === EnumWrapperCategory || treeNode.category === DiagramWrapperCategory || treeNode.category === GojsNodeCategory.Project || !this.hasDiagram()) {\n        this._propertyService.setPropertyData(null);\n        return;\n      }\n      this._selectedTreeNode.set(treeNode);\n      if (!this.isDiagram(treeNode.data) && treeNode.data) {\n        this._propertyService.setPropertyData(treeNode.data);\n        if (event.ctrlKey || event.metaKey) {\n          // Toggle selection for ctrl/cmd + click\n          this.selection.toggle(treeNode);\n        } else if (event.shiftKey && this.selection.selected.length > 0) {\n          // Handle shift + click for range selection\n          // This is a basic implementation - you might want to enhance it based on your needs\n          const lastSelected = this.selection.selected[this.selection.selected.length - 1];\n          const nodes = this.getAllNodes();\n          const startIdx = nodes.indexOf(lastSelected);\n          const endIdx = nodes.indexOf(treeNode);\n          const range = nodes.slice(Math.min(startIdx, endIdx), Math.max(startIdx, endIdx) + 1);\n          range.forEach(n => {\n            if (n.category === ClassWrapperCategory || n.category === EnumWrapperCategory || n.category === DiagramWrapperCategory || n.category === GojsNodeCategory.Project || n.category === GojsNodeCategory.Diagram) {\n              return;\n            }\n            this.selection.select(n);\n          });\n        } else {\n          // Single selection\n          this.selection.clear();\n          this.selection.select(treeNode);\n        }\n      }\n      if (treeNode.category === GojsNodeCategory.Diagram && this.currentDiagram()?.id !== treeNode.data?.id) {\n        this._propertyService.setPropertyData(null);\n        const diagram = treeNode.data;\n        this.diagramUtils.setActiveDiagram(diagram);\n        // Update the URL to include the selected diagram ID\n        const projectId = diagram.idProject;\n        const diagramId = diagram.id;\n        if (projectId && diagramId) {\n          this.router.navigate([`/editor/${projectId}/diagram/${diagramId}`], {\n            replaceUrl: true // Replace the current URL instead of adding a new history entry\n          });\n          // The expandNodeByTag will be called automatically when the diagram is activated\n          // through the activeDiagramChanges subscription in ngOnInit\n        }\n      }\n    }\n    // Helper method to get all nodes in a flat array\n    getAllNodes() {\n      const nodes = [];\n      const getNodesRecursively = nodesList => {\n        nodesList.forEach(node => {\n          nodes.push(node);\n          if (node.children) {\n            getNodesRecursively(node.children);\n          }\n        });\n      };\n      getNodesRecursively(this.dataSource.data);\n      return nodes;\n    }\n    isDiagram(data) {\n      return data.id !== undefined && data.idProject !== undefined && data.category === undefined;\n    }\n    onDragStart(event, node) {\n      if (event.dataTransfer) {\n        event.dataTransfer.setData('text/plain', JSON.stringify(node));\n        this._draggedNode.set(node);\n        this._isDragging.set(true);\n        // Add a class to the dragged element\n        const element = event.target;\n        element.classList.add('dragging');\n      }\n    }\n    onAction(action) {\n      const currentNode = this._currentNode();\n      if (currentNode) {\n        this._contextMenuActionService.executeAction(action, currentNode, this.selection);\n      }\n    }\n    onDragOver(event, node) {\n      event.preventDefault();\n      if (this.isValidDropTarget(node)) {\n        event.dataTransfer.dropEffect = 'move';\n      } else {\n        event.dataTransfer.dropEffect = 'none';\n      }\n    }\n    // Handle the node drop\n    onMove(event, targetNode) {\n      event.preventDefault();\n      this._isDraggedOver.set(false);\n      this._isDragging.set(false);\n      this._currentDragTarget.set(null);\n      if (this.isValidDropTarget(targetNode)) {\n        const draggedNode = this.draggedNode();\n        if (draggedNode) {\n          this.treeNodeService.moveNode(targetNode, draggedNode);\n        }\n      }\n    }\n    isSelected(node) {\n      return this.selectedNodes().has(node);\n    }\n    onRenameF2Key() {\n      const selectedTreeNode = this.selectedTreeNode();\n      if (this.hasEditAccessOnly() && selectedTreeNode && this.hasDiagram()) {\n        this.enableRename(selectedTreeNode);\n      }\n    }\n    canRename(node) {\n      if (node.category == DiagramWrapperCategory || node.category == ClassWrapperCategory || node.category == EnumWrapperCategory || node.category === GojsNodeCategory.Project) return false;else return true;\n    }\n    enableRename(node) {\n      if (!this.hasEditAccessOnly() || !this.hasDiagram()) return;\n      if (this.canRename(node)) {\n        node.isRenaming = true;\n        this._previousName.set(node.name);\n      }\n      // Set a slight timeout to focus on the input after it appears\n      setTimeout(() => {\n        const inputElement = document.querySelector('.rename-input');\n        if (inputElement && this.selectedTreeNode()) {\n          inputElement.focus();\n          inputElement.select(); // Select text to allow easy overwrite\n        }\n      });\n    }\n    saveRename(node, newName) {\n      const trimmedName = newName.trim();\n      const previousName = this.previousName();\n      if (trimmedName === '' || trimmedName === previousName) {\n        // If input is empty or only whitespace, restore the previous name\n        node.name = previousName || node.name;\n        const inputElement = document.querySelector('.rename-input');\n        if (inputElement) {\n          inputElement.blur();\n        }\n      } else {\n        // Update with new name if valid\n        node.name = trimmedName;\n        this._contextMenuActionService.renameNode(node);\n      }\n      node.isRenaming = false;\n      this._previousName.set('');\n    }\n    cancelRename(node) {\n      node.isRenaming = false;\n    }\n    shouldShowTooltip(name, length) {\n      return name.length > length;\n    }\n    onDragEnter(event, node) {\n      event.preventDefault();\n      this._currentDragTarget.set(node);\n      this._isDraggedOver.set(true);\n    }\n    onDragLeave(event, node) {\n      event.preventDefault();\n      if (this.currentDragTarget() === node) {\n        this._isDraggedOver.set(false);\n        this._currentDragTarget.set(null);\n      }\n    }\n    isValidDropTarget(node) {\n      const draggedNode = this.draggedNode();\n      return !!(draggedNode && node.supportingNodes?.includes(draggedNode.category) && !node.category.includes('Wrapper') && this.hasDiagram() && this.canDiagramDraggable(draggedNode));\n    }\n    ngOnDestroy() {\n      this._isDraggedOver.set(false);\n      this._isDragging.set(false);\n      this._currentDragTarget.set(null);\n    }\n    /**\n     * Expands the tree to show a node with the given tag\n     * Also expands all parent nodes to make the node visible\n     * @param tag The tag of the node to expand to\n     */\n    expandNodeByTag(tag) {\n      // Ensure we have tree data before trying to expand\n      if (!this.dataSource.data || this.dataSource.data.length === 0) {\n        // Retry expansion after a delay if tree data is not ready\n        setTimeout(() => this.expandNodeByTag(tag), 200);\n        return;\n      }\n      const node = this.treeNodeService.findNodeByTag(tag);\n      if (node) {\n        // First expand all parent nodes to make this node visible\n        this.expandParentNodes(node);\n        // If the node itself has children, expand it too\n        if (this.hasChild(0, node) && !this.treeControl.isExpanded(node)) {\n          this.treeControl.expand(node);\n        }\n        // Scroll to the node to make it visible in the viewport\n        setTimeout(() => {\n          const nodeElement = document.querySelector(`[data-node-tag=\"${tag}\"]`);\n          if (nodeElement) {\n            nodeElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center'\n            });\n          }\n        }, 200);\n      }\n    }\n    /**\n     * Expands all parent nodes of the given node\n     * @param node The node whose parents should be expanded\n     */\n    expandParentNodes(node) {\n      // Get the parent chain from root to the node\n      const parentChain = this.getParentChain(node);\n      // Expand each parent in the chain\n      parentChain.forEach(parent => {\n        if (!this.treeControl.isExpanded(parent)) {\n          this.treeControl.expand(parent);\n        }\n      });\n    }\n    /**\n     * Gets the chain of parent nodes from root to the given node\n     * @param node The node to find parents for\n     * @returns Array of parent nodes in order from root to immediate parent\n     */\n    getParentChain(node) {\n      const parentChain = [];\n      let currentNode = node;\n      // Traverse up the tree until we reach the root\n      while (currentNode.parentTag) {\n        const parentNode = this.treeNodeService.findNodeByTag(currentNode.parentTag);\n        if (parentNode) {\n          parentChain.unshift(parentNode); // Add to beginning of array\n          currentNode = parentNode;\n        } else {\n          break;\n        }\n      }\n      return parentChain;\n    }\n    static #_ = this.ɵfac = function LibraryTreeComponent_Factory(t) {\n      return new (t || LibraryTreeComponent)(i0.ɵɵdirectiveInject(i1.MatDialog), i0.ɵɵdirectiveInject(i2.TreeNodeService), i0.ɵɵdirectiveInject(i3.DiagramUtils), i0.ɵɵdirectiveInject(i4.ContextMenuActionService), i0.ɵɵdirectiveInject(i5.AccessService), i0.ɵɵdirectiveInject(i6.PropertyService), i0.ɵɵdirectiveInject(i7.NavbarService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i8.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LibraryTreeComponent,\n      selectors: [[\"app-library-tree\"]],\n      hostBindings: function LibraryTreeComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keyup.delete\", function LibraryTreeComponent_keyup_delete_HostBindingHandler($event) {\n            return ctx.onDeleteKey($event);\n          }, false, i0.ɵɵresolveWindow)(\"click\", function LibraryTreeComponent_click_HostBindingHandler() {\n            return ctx.documentClick();\n          }, false, i0.ɵɵresolveDocument)(\"keyup.f2\", function LibraryTreeComponent_keyup_f2_HostBindingHandler() {\n            return ctx.onRenameF2Key();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        expandNodeTag: \"expandNodeTag\"\n      },\n      decls: 10,\n      vars: 9,\n      consts: [[\"class\", \"search-container\", \"backgroundColor\", \"white\", 3, \"placeholder\", \"searchChanged\", 4, \"ngIf\"], [\"class\", \"diagrams-list-view\", 4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\", \"ngIfElse\"], [\"notFoundTemplate\", \"\"], [3, \"hidden\"], [1, \"library-tree\", 3, \"dataSource\", \"treeControl\"], [\"matTreeNodeToggle\", \"\", \"class\", \"tree-node node-content\", 3, \"selected\", \"selected-diagram\", \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\", 4, \"matTreeNodeDef\"], [\"class\", \"tree-node\", 3, \"click\", 4, \"matTreeNodeDef\", \"matTreeNodeDefWhen\"], [4, \"ngIf\"], [\"backgroundColor\", \"white\", 1, \"search-container\", 3, \"placeholder\", \"searchChanged\"], [1, \"diagrams-list-view\"], [\"class\", \"diagram-list-item\", 3, \"selected-diagram\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"diagram-list-item\", 3, \"click\"], [1, \"nodeIcon\", 3, \"innerHTML\"], [3, \"matTooltip\"], [1, \"search-results\"], [\"class\", \"search-result-item\", 3, \"selected\", \"selected-diagram\", \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-result-item\", 3, \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\"], [4, \"ngIf\", \"ngIfElse\"], [\"renameInput\", \"\"], [\"type\", \"text\", 1, \"rename-input\", 3, \"ngModel\", \"ngModelChange\", \"blur\", \"keydown.enter\", \"keydown.escape\"], [\"renameInputElement\", \"\"], [\"class\", \"search-results\", 4, \"ngIf\"], [1, \"not-found-text\"], [\"matTreeNodeToggle\", \"\", 1, \"tree-node\", \"node-content\", 3, \"draggable\", \"contextmenu\", \"dragstart\", \"click\", \"dragover\", \"drop\", \"dblclick\"], [1, \"node-content\", 3, \"click\"], [1, \"nodeIcon\", 3, \"innerHTML\", \"draggable\"], [1, \"tree-node\", 3, \"click\"], [1, \"mat-tree-node\", \"node-content\", 3, \"draggable\", \"click\", \"contextmenu\", \"dragstart\", \"dragenter\", \"dragleave\", \"dragover\", \"drop\", \"dblclick\"], [\"matTreeNodeToggle\", \"\"], [1, \"node-content\"], [\"nestedRenameInput\", \"\"], [\"role\", \"group\"], [\"matTreeNodeOutlet\", \"\"], [1, \"menu-link\", \"mat-elevation-z4\", 3, \"ngStyle\"], [\"mat-menu-item\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-menu-item\", \"\", 3, \"click\"]],\n      template: function LibraryTreeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LibraryTreeComponent_app_search_bar_0_Template, 2, 3, \"app-search-bar\", 0);\n          i0.ɵɵtemplate(1, LibraryTreeComponent_div_1_Template, 6, 4, \"div\", 1);\n          i0.ɵɵtemplate(2, LibraryTreeComponent_div_2_Template, 3, 1, \"div\", 2);\n          i0.ɵɵtemplate(3, LibraryTreeComponent_ng_template_3_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"mat-tree\", 5);\n          i0.ɵɵtemplate(7, LibraryTreeComponent_mat_tree_node_7_Template, 6, 10, \"mat-tree-node\", 6);\n          i0.ɵɵtemplate(8, LibraryTreeComponent_mat_nested_tree_node_8_Template, 11, 20, \"mat-nested-tree-node\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, LibraryTreeComponent_ng_container_9_Template, 3, 2, \"ng-container\", 8);\n        }\n        if (rf & 2) {\n          const _r3 = i0.ɵɵreference(4);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOnlyDiagrams());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showOnlyDiagrams());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchResults().length > 0 && ctx.isSearching() && !ctx.showOnlyDiagrams())(\"ngIfElse\", _r3);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", ctx.isSearching() || ctx.showOnlyDiagrams());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"dataSource\", ctx.dataSource)(\"treeControl\", ctx.treeControl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"matTreeNodeDefWhen\", ctx.hasChild);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDisplayContextMenu() && ctx.hasEditAccessOnly());\n        }\n      },\n      dependencies: [i9.NgForOf, i9.NgIf, i9.NgStyle, i10.MatTooltip, i11.MatIcon, i12.MatMenuItem, i13.MatNestedTreeNode, i13.MatTreeNodeDef, i13.MatTreeNodeToggle, i13.MatTree, i13.MatTreeNode, i13.MatTreeNodeOutlet, i14.MatList, i14.MatListItem, i15.DefaultValueAccessor, i15.NgControlStatus, i15.NgModel, i16.SearchBarComponent, i17.TranslatePipe, i18.TruncatePipe],\n      styles: [\".library-tree[_ngcontent-%COMP%]{padding-inline:.2rem;background-color:transparent;font-size:13px;align-items:center;max-height:300px}.library-tree-invisible[_ngcontent-%COMP%]{display:none}.library-tree[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .library-tree[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-top:0;margin-bottom:0;list-style-type:none}.library-tree[_ngcontent-%COMP%]   .mat-nested-tree-node[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%]{padding-left:.9rem}.library-tree[_ngcontent-%COMP%]   div[role=group][_ngcontent-%COMP%] > .mat-tree-node[_ngcontent-%COMP%]{padding-left:1.4rem;margin-top:.18rem}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]:hover{background-color:#0000000a}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%]{border-radius:4px;cursor:pointer;display:flex;justify-content:flex-start;align-items:center;transition:background-color .3s ease}.library-tree[_ngcontent-%COMP%]   .tree-node[_ngcontent-%COMP%]   .node-content[_ngcontent-%COMP%]:hover{background-color:#e0e0e0}.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]{background-color:#add8e6}.library-tree[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]:hover{background-color:#00000014}.library-tree[_ngcontent-%COMP%]   .selected-diagram[_ngcontent-%COMP%]{background-color:#00000017}.mat-tree-node[_ngcontent-%COMP%]{min-height:1.5rem;padding-bottom:.2rem;z-index:100000}.nodeIcon[_ngcontent-%COMP%]{height:13px;width:13px;margin-right:.5rem;font-family:FontAwesome}.rename-input[_ngcontent-%COMP%]{width:100%;background-color:#fff;border:1px solid #0069b4;padding:.2rem;transition:box-shadow .3s ease}.menu-link[_ngcontent-%COMP%]{background-color:#fff;border-radius:4px;font-size:13px;box-shadow:0 2px 10px #0003;position:absolute;z-index:1000;cursor:pointer}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;max-height:-moz-fit-content;max-height:fit-content;height:1rem;border:none;cursor:pointer}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.menu-link[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:focus{outline:none;background-color:#e0e0e0}.drag-root[_ngcontent-%COMP%]{position:relative;width:100vw;height:100vh}.cdk-drag-preview[_ngcontent-%COMP%]{background-color:#0000001a;border-radius:4px}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.search-container[_ngcontent-%COMP%]{top:0;position:sticky}.search-results[_ngcontent-%COMP%]{max-height:276px;overflow-y:auto;font-size:13px}.search-result-item[_ngcontent-%COMP%]{cursor:pointer;height:32px!important;transition:background-color .2s}.search-result-item[_ngcontent-%COMP%]:hover{background-color:#0000000a}.search-result-item.selected[_ngcontent-%COMP%]{background-color:#00000014}.result-path[_ngcontent-%COMP%]{font-size:.85em;opacity:.7}.not-found-text[_ngcontent-%COMP%]{margin-block:.5rem;padding-inline:.7rem}.diagrams-list-view[_ngcontent-%COMP%]{padding:8px 0;margin-top:8px;max-height:calc(100vh - 200px);overflow-y:auto;font-size:14px}.diagrams-list-view[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{padding:0 16px;margin:0 0 8px;font-size:16px;font-weight:500;color:#000000de}.diagrams-list-view[_ngcontent-%COMP%]   .mat-list[_ngcontent-%COMP%]{padding:0}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:4px 16px;cursor:pointer;transition:background-color .2s ease;height:36px!important}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]:hover{background-color:#0000000a}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item.selected-diagram[_ngcontent-%COMP%]{background-color:#1976d21f;color:#1976d2}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%]{margin-right:8px}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   .nodeIcon[_ngcontent-%COMP%]     svg{width:20px;height:20px}.diagrams-list-view[_ngcontent-%COMP%]   .diagram-list-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}  .diagrams-list-view .mat-list-item-content{padding:0!important}.selected-diagram[_ngcontent-%COMP%]{background-color:#1976d21f;color:#1976d2;font-weight:500}.drop-target[_ngcontent-%COMP%]{position:relative}.drop-target.can-drop[_ngcontent-%COMP%]{border-radius:4px;transition:all .2s ease}.drop-target.can-drop[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;pointer-events:none}.drop-target.cannot-drop[_ngcontent-%COMP%]{position:relative}.drop-target.cannot-drop[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;inset:0;border-radius:4px;pointer-events:none}.dragging[_ngcontent-%COMP%]{cursor:move}\"]\n    });\n  }\n  return LibraryTreeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}