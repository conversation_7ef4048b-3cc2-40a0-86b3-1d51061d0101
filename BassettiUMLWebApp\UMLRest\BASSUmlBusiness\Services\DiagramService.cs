using BASSUmlBusiness.Exceptions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Services.Abstractions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.IdentityModel.Tokens;

namespace BASSUmlBusiness.Services
{
    public class DiagramService : IDiagramService
    {
        private readonly UmlDbContext _context;
        private readonly IProjectService _projectService;
        private readonly IPermissionService _permissionService;
        public DiagramService(UmlDbContext context, IProjectService projectService, IPermissionService permissionService)
        {
            _context = context;
            _projectService = projectService;
            _permissionService = permissionService;
        }

        public Diagram GetDiagramDetails(int idDiagram, string email)
        {
            Diagram existingDiagram = GetDiagram(idDiagram);
            // Validate user has at least view permission for this project
            _permissionService.ValidateUserPermissionOrThrow(email, existingDiagram.IdProject, BASSUmlBusiness.MetaData.AccessType.View);

            Diagram? diagram = GetDiagramsDetails(new List<int> { idDiagram }).FirstOrDefault();
            return diagram;
        }
        public IList<Diagram> GetProjectDiagramsWithDetails(int idProject, string email)
        {
            // Validate user has at least view permission for this project
            _permissionService.ValidateUserPermissionOrThrow(email, idProject, BASSUmlBusiness.MetaData.AccessType.View);

            IList<int> idDiagrams = _context.Diagrams.Where(d => d.IdProject == idProject).Select(d => d.Id).ToList();
            return GetDiagramsDetails(idDiagrams);
        }

        /// <summary>
        /// Retrieves detailed information for a list of diagrams based on their unique identifiers.
        /// </summary>
        /// <param name="idDiagrams">A list of unique identifiers for the diagrams whose details are to be retrieved.</param>
        /// <returns>A list of diagrams with detailed information corresponding to the specified identifiers.</returns>
        private IList<Diagram> GetDiagramsDetails(IList<int> idDiagrams)
        {
            // Fetch diagrams
            IList<Diagram> diagrams = _context.Diagrams
                 .Include(dig => dig.Comments.Where(cmnt => !cmnt.IsDeleted))
                 .Where(d => idDiagrams.Contains(d.Id)).OrderBy(dia => dia.Name).ToList();

            // Fetch classes for these diagrams
            IList<Class> classes = _context.Classes
                .Where(cls => idDiagrams.Contains(cls.IdDiagram) && !cls.IsDeleted)
                .GroupJoin(
                    _context.Attributes,
                    cls => cls.IdTemplateClass,
                    attr => attr.IdTemplateClass,
                    (cls, attributes) => new Class
                    {
                        Id = cls.Id,
                        Name = cls.Name,
                        Property = cls.Property,
                        Attributes = attributes.OrderBy(attr => attr.Name).ToList(),
                        IdDiagram = cls.IdDiagram,
                        IdTemplateClass = cls.IdTemplateClass,
                        Key = cls.Key,
                        Tag = cls.TemplateClass.Tag,
                        Description = cls.TemplateClass.Description,
                        Volumetry = cls.TemplateClass.Volumetry,
                        IsAssociative = cls.TemplateClass.IsAssociative
                    }).OrderBy(cls=>cls.Name).ToList();

            // Fetch enumerations for these diagrams
            IList<Enumeration> enumerations = _context.Enumerations
                .Where(en => idDiagrams.Contains(en.IdDiagram) && !en.IsDeleted)
                .GroupJoin(
                    _context.EnumerationLiterals,
                    enu => enu.IdTemplateEnumeration,
                    enuLit => enuLit.IdTemplateEnumeration,
                    (enu, enuLit) => new Enumeration
                    {
                        Id = enu.Id,
                        Name = enu.Name,
                        Property = enu.Property,
                        Key = enu.Key,
                        EnumerationLiterals = enuLit.OrderBy(lit => lit.Name).ToList(),
                        IdDiagram = enu.IdDiagram,
                        IdTemplateEnumeration = enu.IdTemplateEnumeration,
                        Tag = enu.TemplateEnumeration.Tag,
                        Description = enu.TemplateEnumeration.Description,
                        Volumetry = enu.TemplateEnumeration.Volumetry
                    }).OrderBy(enu => enu.Name).ToList();

            IList<LinkHistory> linkHistories = _context.LinkHistories.Where(lh => idDiagrams.Contains((int)lh.IdDiagram)).ToList();
            // Assign classes and enumerations to the corresponding diagrams
            foreach (Diagram diagram in diagrams)
            {
                diagram.Classes = classes.Where(cls => cls.IdDiagram == diagram.Id).ToList();
                diagram.Enumerations = enumerations.Where(en => en.IdDiagram == diagram.Id).ToList();
                diagram.LinkHistories = linkHistories.Where(lh => lh.IdDiagram == diagram.Id).ToList();
            }
            return diagrams;
        }


        public Diagram GetDiagram(int idDiagram)
        {
            Diagram? diagram = _context.Diagrams.Find(idDiagram);
            return diagram ?? throw new NotFoundException(MetaData.NotFoundTypes.Identifier, "Diagram");
        }
        public Diagram CreateDiagram(Diagram diagram, string email)
        {
            // Validate user has edit permission for this project
            _permissionService.ValidateUserPermissionOrThrow(email, diagram.IdProject, BASSUmlBusiness.MetaData.AccessType.Edit);

            using IDbContextTransaction transaction = _context.BeginTransaction();
            try
            {
                if (!diagram.Name.IsNullOrEmpty())
                {
                    if (diagram.IdFolder == 0) diagram.IdFolder = null;
                    else diagram.Folder = GetFolder(diagram.IdFolder);
                    _projectService.GetProject(diagram.IdProject);
                    _context.Diagrams.Add(diagram);
                    _context.SaveChanges();
                    transaction.Commit();
                    return diagram;
                }
                else throw new BadRequestException("Diagram name can not be null", BadRequestException.NameCannotbeNull);
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw ex;
            }
        }
        public Diagram UpdateDiagram(Diagram diagram, string email)
        {
            if (!diagram.Name.IsNullOrEmpty())
            {
                Diagram? existingDiagram = GetDiagram(diagram.Id);
                // Validate user has edit permission for this project
                _permissionService.ValidateUserPermissionOrThrow(email, existingDiagram.IdProject, AccessType.Edit);

                existingDiagram.Name = diagram.Name;
                _context.SaveChanges();
                return existingDiagram;
            }
            else throw new BadRequestException("Diagram name can not be null", BadRequestException.NameCannotbeNull);
        }

        public void DeleteDiagram(List<int> diagramIds, string email)
        {
            List<Diagram> diagrams = _context.Diagrams.Where(dia => diagramIds.Contains(dia.Id)).ToList();

            // Validate user has edit permission for all projects containing these diagrams
            foreach (var diagram in diagrams)
            {
                _permissionService.ValidateUserPermissionOrThrow(email, diagram.IdProject, BASSUmlBusiness.MetaData.AccessType.Edit);
            }

            IList<LinkHistory> linkHistory = _context.LinkHistories.Where(lnk => diagramIds.Contains((int)lnk.IdDiagram)).ToList();
            IList<LinkPort> linkPort = _context.LinkPorts.Where(lp => diagramIds.Contains((int)lp.IdDiagram)).ToList();
            IList<Comment> comments = _context.Comments.Where(dia => diagramIds.Contains(dia.IdDiagram)).ToList();

            if (linkPort.Any())
                _context.LinkPorts.RemoveRange(linkPort);
            if (linkHistory.Any())
                _context.LinkHistories.RemoveRange(linkHistory);
            if (comments.Any())
                _context.Comments.RemoveRange(comments);
            _context.Diagrams.RemoveRange(diagrams);
            _context.SaveChanges();
        }

        /// <summary>
        /// Retrive a folder from database by its id
        /// </summary>
        /// <param name="idFolder">Identifier of the folder</param>
        /// <returns>Folder object</returns>
        /// <exception cref="NotFoundException"></exception>
        private Folder GetFolder(int? idFolder)
        {
            Folder folder = _context.Folders.Find(idFolder) ?? throw new NotFoundException(MetaData.NotFoundTypes.Identifier, "Folder");
            return folder;
        }

        public Diagram MoveToFolder(Diagram diagram)
        {
            if (diagram == null || diagram.Id == 0 || diagram.IdFolder == 0)
                throw new ArgumentNullException("Diagram Id or Destination Folder Id cannot be null or 0");
            Diagram existingDiagram = GetDiagram(diagram.Id);
            GetFolder(diagram.IdFolder);
            using IDbContextTransaction _transaction = _context.BeginTransaction();
            try
            {
                existingDiagram.IdFolder = diagram.IdFolder;
                _context.SaveChanges();
                _transaction.Commit();
                return existingDiagram;
            }
            catch (Exception ex)
            {
                _transaction.Rollback();
                throw;
            }
        }
        public Diagram RemoveFromFolder(int IdDiagram)
        {
            Diagram diagram = GetDiagram(IdDiagram);
            if (diagram.IdFolder == 0) throw new BadRequestException("Diagram has no parent Folder", BadRequestException.EntityHasNoParentFolder);
            Folder folder = GetFolder(diagram.IdFolder);
            using IDbContextTransaction _transaction = _context.BeginTransaction();
            try
            {
                diagram.IdProject = folder.IdProject;
                diagram.IdFolder = null;
                _context.SaveChanges();
                _transaction.Commit();
                return diagram;
            }
            catch (Exception ex)
            {
                _transaction.Rollback();
                throw;
            }
        }
    }
}
