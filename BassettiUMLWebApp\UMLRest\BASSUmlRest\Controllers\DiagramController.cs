using AutoMapper;
using BASSUmlBusiness.Models;
using BASSUmlBusiness.Services.Abstractions;
using BASSUmlRest.DTOs.DiagramDTOs;
using BASSUmlRest.DTOs.FolderDTOs;
using Microsoft.AspNetCore.Mvc;

namespace BASSUmlRest.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DiagramController : ControllerBase
    {
        private readonly IDiagramService _diagramService;
        private readonly ICommentService _commentService;
        private readonly IMapper _mapper;
        public DiagramController(IDiagramService diagramService, IMapper mapper, ICommentService commentService)
        {
            _diagramService = diagramService;
            _mapper = mapper;

        }
        #region Diagram
        /// <summary>
        /// Retrieves detailed information about a diagram including its associated classes with links and attributes.
        /// </summary>
        /// <param name="idDiagram">Require a diagram Id</param>
        /// <returns>List of classes</returns>
        /// <response code="403">When user lacks view permission for the project</response>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">The diagram with its associated classes and attributes</response>
        [HttpGet("{idDiagram}")]
        public ActionResult<DiagramDetailsDTO> GetDiagramDetails(int idDiagram)
        {
            Diagram diagram = _diagramService.GetDiagramDetails(idDiagram, User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<Diagram, DiagramDetailsDTO>(diagram));
        }

        /// <summary>
        /// Retrieves a list of diagrams associated with a specific project, including detailed information for each diagram.
        /// </summary>
        /// <param name="idProject">Require a project Id</param>
        /// <returns>List of classes</returns>
        /// <response code="403">When user lacks view permission for the project</response>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">The list of diagrams with its associated classes and attributes</response>
        [HttpGet("project/{idProject}")]
        public ActionResult<IList<DiagramDetailsDTO>> GetProjectDiagramsWithDetails(int idProject)
        {
            IList<Diagram> diagram = _diagramService.GetProjectDiagramsWithDetails(idProject, User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<IList<Diagram>, IList<DiagramDetailsDTO>>(diagram));
        }

        /// <summary>
        /// Create a diagram object
        /// </summary>
        /// <param name="diagram">Require a Diagram as JSON</param>
        /// <returns>Newly created <see cref="DiagramDTO">Diagram</see> object </returns>
        /// <response code="403">When user lacks edit permission for the project</response>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns a JSON of Diagram object</response>
        [HttpPost]
        public ActionResult<DiagramDTO> CreateDiagram([FromBody] DiagramDTO diagram)
        {
            Diagram createdDiagram = _diagramService.CreateDiagram(_mapper.Map<DiagramDTO, Diagram>(diagram), User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<Diagram, DiagramDTO>(createdDiagram));
        }
        /// <summary>
        /// Update an existing diagram data
        /// </summary>
        /// <param name="diagram">Require a diagram object as JSON</param>
        /// <returns>Newly updated <see cref="DiagramInfoDTO">Diagram</see> object </returns>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="403">When user lacks edit permission for the project</response>
        /// <response code="500">When got any error in database</response>
        /// <response code="200">Returns a JSON of Project object</response>
        [HttpPatch]
        public ActionResult<DiagramInfoDTO> UpdateDiagram([FromBody] DiagramInfoDTO diagram)
        {
            Diagram updatedDiagram = _diagramService.UpdateDiagram(_mapper.Map<DiagramInfoDTO, Diagram>(diagram), User.GetEmployeeEmail() ?? User.GetEmail());
            return Ok(_mapper.Map<Diagram, DiagramInfoDTO>(updatedDiagram));
        }
        /// <summary>
        /// Delete a particular diagram depending on the id diagram parameter (cascade delete)
        /// </summary>
        /// <param name="idDiagram">Require a diagram Id</param>
        /// <returns>No Content Result</returns>
        /// <response code="204">Returns No Content Result</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="403">When user lacks edit permission for the project</response>
        /// <response code="500">When got any error in database</response>
        [HttpDelete("{idDiagram}")]
        public ActionResult<bool> DeleteDiagram(int idDiagram)
        {
            _diagramService.DeleteDiagram(new List<int>() { idDiagram }, User.GetEmployeeEmail() ?? User.GetEmail());
            return NoContent();
        }

        /// <summary>
        /// Move a diagram to a folder
        /// </summary>
        /// <param name="moveToFolder">Required <see cref="MoveToFolderDTO">Diagram</see> object as json</param>
        /// <returns>Updated <see cref="DiagramInfoDTO">diagram</see> as Json</returns>
        /// <response code="200">Returns a JSON of updated Diagram object</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        [HttpPatch("moveToFolder")]
        public ActionResult<DiagramInfoDTO> MoveToFolder([FromBody] MoveToFolderDTO moveToFolder)
        {
            Diagram diagram = _diagramService.MoveToFolder(_mapper.Map<MoveToFolderDTO, Diagram>(moveToFolder));
            return Ok(_mapper.Map<Diagram, DiagramInfoDTO>(diagram));
        }

        /// <summary>
        /// Remove a diagram from a folder to its project
        /// </summary>
        /// <param name="removeFromFolderDTO">Identifier of the <see cref="RemoveFromFolderDTO">diagram</see> inside json object</param>
        /// <returns>Updated <see cref="DiagramInfoDTO">Diagram</see> as Json</returns>
        /// <response code="200">Returns a JSON of updated Diagram object</response>
        /// <response code="404">When not found the object of the id in database</response>
        /// <response code="500">When got any error in database</response>
        [HttpPatch("removeFromFolder")]
        public ActionResult<DiagramInfoDTO> RemoveFromFolder([FromBody] RemoveFromFolderDTO removeFromFolderDTO)
        {
            Diagram diagram = _diagramService.RemoveFromFolder(removeFromFolderDTO.Id);
            return Ok(_mapper.Map<Diagram, DiagramInfoDTO>(diagram));
        }
        #endregion


    }
}


