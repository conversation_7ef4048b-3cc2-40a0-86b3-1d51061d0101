<app-search-bar
  class="search-container"
  *ngIf="!showOnlyDiagrams()"
  (searchChanged)="onSearch($event)"
  placeholder="{{ 'diagram.search' | translate }}"
  backgroundColor="white"
></app-search-bar>

<!-- Diagrams List View (when toggled) -->
<div *ngIf="showOnlyDiagrams()" class="diagrams-list-view">
  <h3>{{ "diagram.diagramsList" | translate }}</h3>
  <!-- <h3>{{ "Diagrams" | translate }}</h3> -->
  <mat-list>
    <mat-list-item
      *ngFor="let diagram of searchResults()"
      class="diagram-list-item"
      [class.selected-diagram]="diagram.tag === currentDiagramTag()"
      (click)="selectDiagramNode($event, diagram)"
    >
      <span class="nodeIcon" [innerHTML]="diagram.icon"></span>
      <span
        [matTooltip]="shouldShowTooltip(diagram.name, 25) ? diagram.name : ''"
      >
        {{ diagram.name | truncate : 25 }}
      </span>
    </mat-list-item>
  </mat-list>
</div>

<!-- Search Results List View -->
<div
  *ngIf="
    searchResults().length > 0 && isSearching() && !showOnlyDiagrams();
    else notFoundTemplate
  "
  class="search-results"
>
  <mat-list>
    <mat-list-item
      *ngFor="let result of searchResults()"
      class="search-result-item"
      [class.selected]="selection.isSelected(result)"
      [class.selected-diagram]="result.tag === currentDiagramTag()"
      (contextmenu)="onRightClick($event, result)"
      [draggable]="result.isDraggable && hasEditAccessOnly() && hasDiagram()"
      (dragstart)="onDragStart($event, result)"
      (click)="selectDiagramNode($event, result)"
      (dragover)="onDragOver($event, result)"
      (drop)="onMove($event, result)"
      (dblclick)="enableRename(result)"
      [attr.data-node-tag]="result.tag"
    >
      <span class="nodeIcon" [innerHTML]="result.icon"></span>
      <ng-container *ngIf="!result.isRenaming; else renameInput">
        <span
          [matTooltip]="shouldShowTooltip(result.name, 25) ? result.name : ''"
        >
          {{ result.name | truncate : 25 }}</span
        >
      </ng-container>
      <ng-template #renameInput>
        <input
          type="text"
          [(ngModel)]="result.name"
          (blur)="saveRename(result, renameInputElement.value)"
          (keydown.enter)="saveRename(result, renameInputElement.value)"
          (keydown.escape)="cancelRename(result)"
          class="rename-input"
          #renameInputElement
        />
      </ng-template>
    </mat-list-item>
  </mat-list>
</div>
<ng-template #notFoundTemplate>
  <div
    *ngIf="searchResults().length === 0 && isSearching() && !showOnlyDiagrams()"
    class="search-results"
  >
    <p class="not-found-text">{{ "diagram.notFound" | translate }}</p>
  </div>
</ng-template>

<!-- Original Tree View (shown when not searching) -->
<div [hidden]="isSearching() || showOnlyDiagrams()">
  <mat-tree
    [dataSource]="dataSource"
    [treeControl]="treeControl"
    class="library-tree"
  >
    <!-- Leaf node -->
    <mat-tree-node
      *matTreeNodeDef="let node"
      matTreeNodeToggle
      class="tree-node node-content"
      [class.selected]="selection.isSelected(node)"
      [class.selected-diagram]="node.tag === currentDiagramTag()"
      (contextmenu)="onRightClick($event, node)"
      [draggable]="node.isDraggable && hasEditAccessOnly() && hasDiagram()"
      (dragstart)="onDragStart($event, node)"
      (click)="selectDiagramNode($event, node)"
      (dragover)="onDragOver($event, node)"
      (drop)="onMove($event, node)"
      (dblclick)="enableRename(node)"
      [attr.data-node-tag]="node.tag"
    >
      <span (click)="treeControl.toggle(node)" class="node-content">
        <span
          class="nodeIcon"
          [innerHTML]="node.icon"
          [draggable]="node.isDraggable && hasEditAccessOnly()"
        ></span>
        <ng-container *ngIf="!node.isRenaming; else renameInput">
          <span
            [matTooltip]="shouldShowTooltip(node.name, 25) ? node.name : ''"
            >{{ node.name | truncate : 25 }}</span
          >
        </ng-container>
        <ng-template #renameInput>
          <input
            type="text"
            [(ngModel)]="node.name"
            (blur)="saveRename(node, renameInputElement.value)"
            (keydown.enter)="saveRename(node, renameInputElement.value)"
            (keydown.escape)="cancelRename(node)"
            class="rename-input"
            #renameInputElement
          />
        </ng-template>
      </span>
    </mat-tree-node>

    <!-- Nested node -->
    <mat-nested-tree-node
      *matTreeNodeDef="let node; when: hasChild"
      class="tree-node"
      (click)="selectDiagramNode($event, node)"
      [attr.data-node-tag]="node.tag"
    >
      <div
        class="mat-tree-node node-content"
        [class.drop-target]="isDraggedOver()"
        [class.can-drop]="isDraggedOver() && isValidDropTarget(node)"
        [class.cannot-drop]="isDraggedOver() && !isValidDropTarget(node)"
        [class.dragging]="isDragging() && draggedNode() === node"
        (click)="treeControl.toggle(node)"
        [class.selected]="selection.isSelected(node)"
        (contextmenu)="onRightClick($event, node)"
        [draggable]="node.isDraggable && hasEditAccessOnly() && hasDiagram()"
        (dragstart)="onDragStart($event, node)"
        (dragenter)="onDragEnter($event, node)"
        (dragleave)="onDragLeave($event, node)"
        (dragover)="onDragOver($event, node)"
        (drop)="onMove($event, node)"
        (dblclick)="enableRename(node)"
      >
        <mat-icon matTreeNodeToggle [attr.aria-label]="'Toggle ' + node.name">
          {{ treeControl.isExpanded(node) ? "expand_more" : "chevron_right" }}
        </mat-icon>

        <span class="node-content">
          <span
            class="nodeIcon"
            [innerHTML]="node.icon"
            [draggable]="node.isDraggable && hasEditAccessOnly()"
          ></span>
          <ng-container *ngIf="!node.isRenaming; else nestedRenameInput">
            <span
              [matTooltip]="shouldShowTooltip(node.name, 25) ? node.name : ''"
              >{{ node.name | truncate : 25 }}</span
            >
          </ng-container>
          <ng-template #nestedRenameInput>
            <input
              type="text"
              [(ngModel)]="node.name"
              (blur)="saveRename(node, renameInputElement.value)"
              (keydown.enter)="saveRename(node, renameInputElement.value)"
              (keydown.escape)="cancelRename(node)"
              class="rename-input"
              #renameInputElement
            />
          </ng-template>
        </span>
      </div>

      <div
        [class.library-tree-invisible]="!treeControl.isExpanded(node)"
        role="group"
      >
        <ng-container matTreeNodeOutlet></ng-container>
      </div>
    </mat-nested-tree-node>
  </mat-tree>
</div>

<!-- Context menu -->
<ng-container *ngIf="isDisplayContextMenu() && hasEditAccessOnly()">
  <div class="menu-link mat-elevation-z4" [ngStyle]="getRightClickMenuStyle()">
    <button
      mat-menu-item
      *ngFor="let option of contextMenuOptions()"
      (click)="onAction(option.actionName)"
    >
      {{ option.label }}
    </button>
  </div>
</ng-container>
