﻿using BASSUmlBusiness.Services.Abstractions;
using BASSUmlBusiness.BusinessModel;
using BASSUmlBusiness.Exceptions;
using BASSUmlBusiness.MetaData;
using BASSUmlBusiness.Models;
using System.Security;

namespace BASSUmlBusiness.Services
{
    public class PermissionService : IPermissionService
    {
        private readonly UmlDbContext _context;
        public PermissionService(UmlDbContext context)
        {
            _context = context;
        }
        public Permission CreatePermission(Permission permission)
        {
            Getproject(permission.IdProject);
            if (permission.AccessType == AccessType.Admin)
            {
                Permission perm = _context.Permissions.FirstOrDefault(perm => perm.IdProject == permission.IdProject && perm.AccessType == AccessType.Admin);
                if (perm != null) throw new BadRequestException("Project owner already exists", BadRequestException.AlreadyExist);
            }
            _context.Permissions.Add(permission);
            _context.SaveChanges();
            return permission;
        }

        public IList<Permission> AddPermissions(AddPermission permission, string email)
        {
            Getproject(permission.IdProject);
            CheckOwnerPermission(email, permission.IdProject);
            IList<Permission> permissions = permission.Editors.Distinct()
            .Select(editor => new Permission
            {
                AccessType = AccessType.Edit,
                Email = editor,
                IdProject = permission.IdProject
            })
            .Concat(permission.Viewers.Distinct()
                .Select(viewer => new Permission
                {
                    AccessType = AccessType.View,
                    Email = viewer,
                    IdProject = permission.IdProject
                })).Concat(permission.Admins.Distinct()
                .Select(owner => new Permission
                {
                    AccessType = AccessType.Admin,
                    Email = owner,
                    IdProject = permission.IdProject
                }))
            .ToList();
            List<Permission> permissionList = GetAllPermissions();
            permissions = permissions.DistinctBy(perm => perm.Email).ToList();
            List<Permission> permissionsToUpdate = new List<Permission>();
            List<Permission> permissionsToAdd = new List<Permission>();
            foreach (Permission perm in permissions)
            {
                Permission existingPermission = permissionList.FirstOrDefault(p => p.Email == perm.Email && p.IdProject == perm.IdProject);
                if (existingPermission != null)
                {
                    existingPermission.AccessType = perm.AccessType;
                    permissionsToUpdate.Add(existingPermission);
                }
                else permissionsToAdd.Add(perm);
            }
            if (permissionsToUpdate.Any())
                _context.Permissions.UpdateRange(permissionsToUpdate);
            if (permissionsToAdd.Any())
                _context.Permissions.AddRange(permissionsToAdd);
            _context.SaveChanges();
            return permissions;
        }
        /// <summary>
        /// Retrieves all permissions.
        /// </summary>
        /// <returns>A list of all permissions.</returns>
        private List<Permission> GetAllPermissions()
        {
            return _context.Permissions.ToList();
        }
        public void DeletePermission(int idPermission, string email)
        {
            Permission permission = _context.Permissions.Find(idPermission) ?? throw new NotFoundException(NotFoundTypes.Identifier, "Permission");
            Permission ownerPermission=CheckOwnerPermission(email, permission.IdProject);
            if (ownerPermission.Id == idPermission) throw new BadRequestException("You can't remove your access", BadRequestException.PermissionRemoved);
            _context.Permissions.Remove(permission);
            _context.SaveChanges();
        }

        public IList<Permission> GetPermissions(int idProject)
        {
            Getproject(idProject);
            IList<Permission> permissions = _context.Permissions.Where(permission => permission.IdProject == idProject).ToList();
            return permissions;
        }

        public List<Permission> UpdatePermission(List<Permission> permissions, string email)
        {
            List<int> perIds = permissions.Select(per => per.Id).ToList();
            List<Permission> permissionList = GetAllPermissions();
            List<Permission> existingPermissions = permissionList.Where(perm => perIds.Contains(perm.Id)).ToList();
            using var transaction = _context.Database.BeginTransaction();
            try
            {
                foreach (Permission permission in existingPermissions)
                {
                    Permission ownerPermission = permissionList.FirstOrDefault(perm => perm.Email == email && perm.IdProject == permission.IdProject && perm.AccessType == AccessType.Admin) ?? throw new SecurityException("Only owner can update permission");
                    Permission updatePermission = permissions.Find(perm => perm.Id == permission.Id);
                    if (updatePermission != null)
                        permission.AccessType = updatePermission.AccessType;
                }
                _context.SaveChanges();
                transaction.Commit();
                return existingPermissions;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw new Exception("Failed to update permission", ex);
            }

        }

        public Permission GetProjectPermissionByUser(int idProject, string email)
        {
            if (string.IsNullOrWhiteSpace(email)) throw new ArgumentNullException("User email can not be null.");
            Getproject(idProject);
            Contact contact = _context.Contacts.SingleOrDefault(cont => cont.Email == email) ?? throw new NotFoundException(NotFoundTypes.Email, "Contact");
            Permission permission = _context.Permissions.FirstOrDefault(perm => perm.IdProject == idProject && perm.Email == email) ?? throw new NotFoundException(NotFoundTypes.Email, "Permission");
            return permission;
        }

        /// <summary>
        /// Get a project with the identifier or throws error
        /// </summary>
        /// <param name="idProject">Project identifier</param>
        /// <returns>Project with the identifier</returns>
        /// <exception cref="NotFoundException"></exception>
        private Project Getproject(int idProject)
        {
            return _context.Projects.Find(idProject) ?? throw new NotFoundException(NotFoundTypes.Identifier, "Project");
        }

        /// <summary>
        /// Check if a user is owner of a project
        /// </summary>
        /// <param name="email">Email id of user</param>
        /// <param name="idProject">Identifier of a project</param>
        /// <returns>Returns the permission object</returns>
        /// <exception cref="SecurityException"></exception>
        private Permission CheckOwnerPermission(string email, int idProject)
        {
            return _context.Permissions.FirstOrDefault(perm => perm.Email == email && perm.IdProject == idProject && perm.AccessType == AccessType.Admin) ?? throw new SecurityException("Only admin can update permission");
        }

        /// <summary>
        /// Validates if a user has the required permission level for a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <param name="requiredAccessType">Minimum required access type</param>
        /// <returns>True if user has required permission, false otherwise</returns>
        public bool ValidateUserPermission(string email, int idProject, AccessType requiredAccessType)
        {
            if (string.IsNullOrWhiteSpace(email)) return false;

            try
            {
                Permission permission = GetProjectPermissionByUser(idProject, email);
                return HasRequiredAccess(permission.AccessType, requiredAccessType);
            }
            catch (NotFoundException)
            {
                return false;
            }
        }

        /// <summary>
        /// Validates if a user has edit or admin access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user can edit the project</returns>
        public bool CanUserEditProject(string email, int idProject)
        {
            return ValidateUserPermission(email, idProject, AccessType.Edit);
        }

        /// <summary>
        /// Validates if a user has admin access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user is admin of the project</returns>
        public bool IsUserProjectAdmin(string email, int idProject)
        {
            return ValidateUserPermission(email, idProject, AccessType.Admin);
        }

        /// <summary>
        /// Validates if a user has at least view access to a project
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <returns>True if user can view the project</returns>
        public bool CanUserViewProject(string email, int idProject)
        {
            return ValidateUserPermission(email, idProject, AccessType.View);
        }

        /// <summary>
        /// Checks if the user access type meets the required access level
        /// </summary>
        /// <param name="userAccessType">User's current access type</param>
        /// <param name="requiredAccessType">Required minimum access type</param>
        /// <returns>True if user has sufficient access</returns>
        private bool HasRequiredAccess(AccessType userAccessType, AccessType requiredAccessType)
        {
            // Admin has all permissions
            if (userAccessType == AccessType.Admin) return true;

            // Edit access includes View permissions
            if (userAccessType == AccessType.Edit && (requiredAccessType == AccessType.Edit || requiredAccessType == AccessType.View)) return true;

            // View access only for View requirements
            if (userAccessType == AccessType.View && requiredAccessType == AccessType.View) return true;

            return false;
        }

        /// <summary>
        /// Validates user permission and throws SecurityException if insufficient
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="idProject">Project ID</param>
        /// <param name="requiredAccessType">Required access type</param>
        /// <exception cref="SecurityException">Thrown when user lacks required permission</exception>
        public void ValidateUserPermissionOrThrow(string email, int idProject, AccessType requiredAccessType)
        {
            if (!ValidateUserPermission(email, idProject, requiredAccessType))
            {
                string accessTypeName = requiredAccessType.ToString().ToLower();
                throw new SecurityException($"User does not have {accessTypeName} access to this project");
            }
        }

    }
}
